# Migracja Proposals z RPC na REST API

## Opis zmian

Zmieniono sposób pobierania proposals z RPC query na REST API w hooku `useVotingData`.

## Zmiany w kodzie

### 1. Zaktualizowano `hooks/voting/useVotingData.ts`

**Przed:**
```typescript
const proposalsQuery = cosmos.gov.v1.useProposals({
  request: {
    voter: "",
    depositor: "",
    pagination: paginate(50n, true),
    proposalStatus: ProposalStatus.PROPOSAL_STATUS_UNSPECIFIED,
  },
  options: {
    enabled: isReady,
    staleTime: Infinity,
    select: ({ proposals }) => processProposals(proposals as any),
  },
});
```

**Po:**
```typescript
// Zastąpienie RPC query na REST API query
const restApiUrl = getRestApiUrl(chainName);

const proposalsQuery = useQuery({
  queryKey: ["proposals", chainName, restApiUrl],
  queryFn: async () => {
    if (!restApiUrl) throw new Error("REST API URL not found");
    const response = await handleGetProposals({ restApiUrl });
    return processProposals(response.proposals as any);
  },
  enabled: Boolean(restApiUrl),
  staleTime: Infinity,
});
```

### 2. Dodano nowy plik `utils/chain.ts`

Funkcje pomocnicze do uzyskiwania endpoints dla chainów:

```typescript
export const getRestApiUrl = (chainName: string): string | undefined => {
  const chain = chains.find((c) => c.chain_name === chainName);
  return chain?.apis?.rest?.[0]?.address;
};
```

### 3. Konfiguracja REST endpoints

REST endpoints są konfigurowane w `config/customChains.ts`:

- **empe**: `https://lcd.empe.io/`
- **empetestnet**: `https://lcd-testnet.empe.io/`
- **empedevnet**: `https://lcd-devnet.empe.io/`

## Korzyści

1. **Lepsze performance** - REST API może być szybsze niż RPC dla niektórych zapytań
2. **Większa niezawodność** - REST endpoints mogą być bardziej stabilne
3. **Łatwiejsze debugowanie** - REST API można łatwo testować w przeglądarce
4. **Zgodność z istniejącą infrastrukturą** - wykorzystuje już istniejące REST endpoints

## Użycie

Hook `useVotingData` działa tak samo jak wcześniej:

```typescript
const { data, isLoading, refetch } = useVotingData(chainName);
```

Automatycznie wybiera odpowiedni REST endpoint na podstawie `chainName`.

## Testowanie

Aby przetestować zmiany:

1. Sprawdź czy proposals ładują się poprawnie dla wszystkich chainów
2. Zweryfikuj czy dane są identyczne z poprzednią implementacją RPC
3. Przetestuj obsługę błędów gdy REST endpoint nie jest dostępny
