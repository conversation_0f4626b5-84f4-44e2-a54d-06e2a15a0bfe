# Migracja Proposals z RPC na REST API

## Opis zmian

Zmieniono sposób pobierania proposals z RPC query na REST API w hooku `useVotingData`.

## Zmiany w kodzie

### 1. Zaktualizowano `hooks/voting/useVotingData.ts`

**Przed:**

```typescript
const proposalsQuery = cosmos.gov.v1.useProposals({
  request: {
    voter: "",
    depositor: "",
    pagination: paginate(50n, true),
    proposalStatus: ProposalStatus.PROPOSAL_STATUS_UNSPECIFIED,
  },
  options: {
    enabled: isReady,
    staleTime: Infinity,
    select: ({ proposals }) => processProposals(proposals as any),
  },
});
```

**Po:**

```typescript
// Zastąpienie RPC query na REST API query
const restApiUrl = getRestApiUrl(chainName);

const proposalsQuery = useQuery({
  queryKey: ["proposals", chainName, restApiUrl],
  queryFn: async () => {
    if (!restApiUrl) throw new Error("REST API URL not found");
    const response = await handleGetProposals({ restApiUrl });
    return processProposals(response.proposals as any);
  },
  enabled: Boolean(restApiUrl),
  staleTime: Infinity,
});
```

### 2. Dodano nowy plik `utils/chain.ts`

Funkcje pomocnicze do uzyskiwania endpoints dla chainów:

```typescript
export const getRestApiUrl = (chainName: string): string | undefined => {
  const chain = chains.find((c) => c.chain_name === chainName);
  return chain?.apis?.rest?.[0]?.address;
};
```

### 3. Konfiguracja REST endpoints

REST endpoints są konfigurowane w `config/customChains.ts`:

- **empe**: `https://lcd.empe.io/`
- **empetestnet**: `https://lcd-testnet.empe.io/`
- **empedevnet**: `https://lcd-devnet.empe.io/`

## Korzyści

1. **Lepsze performance** - REST API może być szybsze niż RPC dla niektórych zapytań
2. **Większa niezawodność** - REST endpoints mogą być bardziej stabilne
3. **Łatwiejsze debugowanie** - REST API można łatwo testować w przeglądarce
4. **Zgodność z istniejącą infrastrukturą** - wykorzystuje już istniejące REST endpoints

## Użycie

Hook `useVotingData` działa tak samo jak wcześniej:

```typescript
const { data, isLoading, refetch } = useVotingData(chainName);
```

Automatycznie wybiera odpowiedni REST endpoint na podstawie `chainName`.

## Rozwiązane problemy

### Problem z wyświetlaniem proposals

Główny problem polegał na różnicy w strukturze danych między RPC a REST API:

1. **Różne formaty dat**: REST API zwraca daty jako stringi, RPC jako obiekty Date
2. **Różne nazwy pól**: REST API używa snake_case (`voting_end_time`), RPC camelCase (`votingEndTime`)
3. **Niezgodność importów**: `ProposalStatus` był importowany z `v1beta1` zamiast `v1`

### Rozwiązanie

Dodano funkcję transformacji `transformRestProposalToRpcFormat` która:

- Konwertuje daty ze stringów na obiekty Date
- Mapuje pola z snake_case na camelCase
- Zachowuje oryginalne pola dla kompatybilności

## Testowanie

### Automatyczne debugowanie

Kod zawiera teraz rozbudowane logowanie:

```javascript
// W konsoli przeglądarki zobaczysz:
// "Fetching proposals from REST API: https://lcd-testnet.empe.io/"
// "Successfully fetched 12 proposals from REST API"
// "Transformed 12 proposals: [...]"
// "Processed 12 proposals after filtering"
// "VotingList for empetestnet: {...}"
```

### Kroki testowania

1. Otwórz Developer Tools (F12) i przejdź do zakładki Console
2. Przejdź do strony z voting
3. Sprawdź logi w konsoli - powinieneś zobaczyć:

   - Pobieranie danych z REST API
   - Liczbę pobranych proposals
   - Transformację danych
   - Wyświetlanie w komponencie

4. Sprawdź czy proposals wyświetlają się poprawnie dla wszystkich chainów:
   - `empe` (mainnet)
   - `empetestnet`
   - `empedevnet`

### Debugowanie problemów

Jeśli proposals nadal się nie wyświetlają:

1. **Sprawdź konsole** - czy są błędy lub ostrzeżenia
2. **Sprawdź Network tab** - czy zapytania do REST API są wysyłane i zwracają dane
3. **Sprawdź logi transformacji** - czy dane są poprawnie przekształcane
4. **Sprawdź filtrowanie** - czy proposals nie są odfiltrowane przez logikę biznesową
