# Local Development and Docker Deployment Guide

## Repository Setup

Before running the project, clone the required repositories and install dependencies:

1. <PERSON>lone the necessary repositories:
   ```sh
   <NAME_EMAIL>:your-org/empe-js.git
   <NAME_EMAIL>:your-org/empe-front-kit.git
   ```

```sh
cd empe-js
yarn install
yarn build
cd ..

cd empe-front-kit
yarn install
yarn build
cd ..
```

## Local Development Setup

To run the project locally, follow these steps:

1. Set the package paths in `package.json`:
   ```json
   "@empe/empejs": "file:../empe-js",
   "@empe/front-kit-next-ui": "file:../empe-front-kit/packages/next-ui",
   "@empe/front-kit-tailwind-config": "file:../empe-front-kit/packages/tailwind-config",
   ```
2. Ensure that the projects `empe-hub`, `empe-front-kit`, and `empe-js` are located in the same directory at the same nesting level.
3. Install dependencies:
   ```sh
   yarn install
   ```
4. Start the development server:
   ```sh
   yarn dev
   ```

## Docker Image Build and Auto Deployment

To build a Docker image and trigger the auto-deployment process:

1. Modify the package paths in `package.json`:

   - Change from:
     ```json
     "@empe/empejs": "file:../empe-js",
     "@empe/front-kit-next-ui": "file:../empe-front-kit/packages/next-ui",
     "@empe/front-kit-tailwind-config": "file:../empe-front-kit/packages/tailwind-config",
     ```
   - To:
     ```json
     "@empe/empejs": "file:./empe-js",
     "@empe/front-kit-next-ui": "file:./empe-front-kit/packages/next-ui",
     "@empe/front-kit-tailwind-config": "file:./empe-front-kit/packages/tailwind-config",
     ```
     _(Replace `..` with `.` in the paths.)_

2. Run the `auto_deploy` script.

## Building a Docker Image Without Auto Deployment

To build the Docker image manually:

1. Move up one directory level:
   ```sh
   cd ..
   ```
2. Run the following command:
   ```sh
   docker build --platform linux/amd64 -t empe-hub -f ./empe-hub/Dockerfile .
   ```

_(Changing the Docker build context allows including all required libraries without copying them inside the project.)_

This is a Cosmos App project bootstrapped with [`create-cosmos-app`](https://github.com/cosmology-tech/create-cosmos-app).

## Getting Started

First, install the packages and run the development server:

```bash
yarn && yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `pages/index.tsx`. The page auto-updates as you edit the file.

## How to connect to Starship chains

1. Follow the official guide to set up Starship: https://docs.cosmology.zone/starship/get-started/step-1
2. Run `yarn starship start` and wait until Starship is up and running
3. Open a new terminal and run `yarn dev`
4. Open http://localhost:3000, select "Osmosis Devnet" or "Cosmos Hub Devnet" from the chain dropdown in the top right corner then click "Connect Wallet" in the left sidebar to connect to the chain
5. Go to "Faucet" to get some test tokens and enjoy!

## Learn More

### Chain Registry

The npm package for the Official Cosmos chain registry. Get chain and token data for you application.

- https://github.com/cosmology-tech/chain-registry

### Cosmology Videos

Checkout more videos for how to use various frontend tooling in the Cosmos!

- https://cosmology.zone/learn

### Cosmos Kit

A wallet connector for the Cosmos ⚛️

- https://github.com/cosmology-tech/cosmos-kit

### Telescope

A "babel for the Cosmos", Telescope is a TypeScript Transpiler for Cosmos Protobufs. Telescope is used to generate libraries for Cosmos blockchains. Simply point to your protobuffer files and create developer-friendly Typescript libraries for teams to build on your blockchain.

- https://github.com/cosmology-tech/telescope

🎥 [Checkout the Telescope video playlist](https://www.youtube.com/watch?v=n82MsLe82mk&list=PL-lMkVv7GZwyQaK6bp6kMdOS5mzosxytC) to learn how to use `telescope`!

### CosmWasm TS Codegen

The quickest and easiest way to interact with CosmWasm Contracts. @cosmwasm/ts-codegen converts your CosmWasm smart contracts into dev-friendly TypeScript classes so you can focus on shipping code.

- https://github.com/CosmWasm/ts-codegen

🎥 [Checkout the CosmWasm/ts-codegen video playlist](https://www.youtube.com/watch?v=D_A5V2PfNLA&list=PL-lMkVv7GZwz1KO3jANwr5W4MoziruXwK) to learn how to use `ts-codegen`!

## Learn More about Next.js

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Related

Checkout these related projects:

- [@cosmology/telescope](https://github.com/cosmology-tech/telescope) Your Frontend Companion for Building with TypeScript with Cosmos SDK Modules.
- [@cosmwasm/ts-codegen](https://github.com/CosmWasm/ts-codegen) Convert your CosmWasm smart contracts into dev-friendly TypeScript classes.
- [chain-registry](https://github.com/cosmology-tech/chain-registry) Everything from token symbols, logos, and IBC denominations for all assets you want to support in your application.
- [cosmos-kit](https://github.com/cosmology-tech/cosmos-kit) Experience the convenience of connecting with a variety of web3 wallets through a single, streamlined interface.
- [create-cosmos-app](https://github.com/cosmology-tech/create-cosmos-app) Set up a modern Cosmos app by running one command.
- [interchain-ui](https://github.com/cosmology-tech/interchain-ui) The Interchain Design System, empowering developers with a flexible, easy-to-use UI kit.
- [starship](https://github.com/cosmology-tech/starship) Unified Testing and Development for the Interchain.

## Credits

🛠 Built by Cosmology — if you like our tools, please consider delegating to [our validator ⚛️](https://cosmology.zone/validator)

## Disclaimer

AS DESCRIBED IN THE LICENSES, THE SOFTWARE IS PROVIDED “AS IS”, AT YOUR OWN RISK, AND WITHOUT WARRANTIES OF ANY KIND.

No developer or entity involved in creating this software will be liable for any claims or damages whatsoever associated with your use, inability to use, or your interaction with other users of the code, including any direct, indirect, incidental, special, exemplary, punitive or consequential damages, or loss of profits, cryptocurrencies, tokens, or anything else of value.
