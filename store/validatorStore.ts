import { create } from "zustand";

interface ValidatorStoreActions {
  setOfferingQrUrl: (offeringQrUrl: string) => void;
}

interface ValidatorStoreState {
  offeringQrUrl: string | null;
}

type ValidatorStore = ValidatorStoreActions & ValidatorStoreState;

export const useValidatorStore = create<ValidatorStore>()((set) => ({
  offeringQrUrl: null,
  setOfferingQrUrl: (offeringQrUrl: string) => {
    set({
      offeringQrUrl,
    });
  },
}));
