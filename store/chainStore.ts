import { create } from "zustand";
import { chainOptions } from "@/config";

interface ChainStoreActions {
  setSelectedChain: (chainName: string) => void;
}

interface ChainStoreState {
  selectedChain: string;
}

type ChainStore = ChainStoreActions & ChainStoreState;

export const defaultChain = chainOptions[0].chain_name;

export const useChainStore = create<ChainStore>()(() => ({
  selectedChain: defaultChain,
  setSelectedChain: (chainName: string) => {
    console.log(`Setting selected chain to: ${chainName}`);
    useChainStore.setState({ selectedChain: chainName });
  },
}));
