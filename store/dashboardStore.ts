import { create } from "zustand";

interface DashboardStoreActions {
  setAllData: (data: Omit<DashboardStoreState, "isLoading">) => void;
  setIsLoading: (isLoading: boolean) => void;
}

interface DashboardStoreState {
  inflation: any;
  totalSupply: any;
  didDocumentCount: any;
  communityPool: any;
  bondedTokens: any;
  tokenBurned: any;
  circulatingSupply: any;
  isLoading: boolean;
}

type DashboardStore = DashboardStoreActions & DashboardStoreState;

export const useDashboardStore = create<DashboardStore>()((set) => ({
  inflation: 0,
  totalSupply: 0,
  didDocumentCount: 0,
  communityPool: 0,
  bondedTokens: 0,
  tokenBurned: 0,
  circulatingSupply: 0,
  isLoading: true,
  setAllData: (data: Omit<DashboardStoreState, "isLoading">) => set(data),
  setIsLoading: (isLoading: boolean) => set({ isLoading }),
}));
