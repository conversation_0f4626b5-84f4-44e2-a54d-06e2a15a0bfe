export const formatNumberToDisplay = (
  num: number | string,
  locale: string = "en-US",
  separator?: string,
  scale: number = 1
): string => {
  const number = Number(num);
  if (isNaN(number)) return "number is invalid";

  const scaledNumber = number / scale;
  const truncatedNumber = Math.floor(scaledNumber * 100) / 100;

  let formatted = truncatedNumber.toLocaleString(locale, { useGrouping: true });

  if (separator) {
    formatted = formatted.replace(/[\s,]/g, separator);
  }

  return formatted;
};
