import React from "react";

export const colorizeSubstring = (
  text: string,
  textToColor: string,
  isGradient: boolean = false
) => {
  const parts = text.split("&***&");

  // Function to handle newlines in text
  const renderWithLineBreaks = (content: string) => {
    return content.split("\n").map((line, index, array) => (
      <React.Fragment key={index}>
        {line}
        {index < array.length - 1 && <br />}
      </React.Fragment>
    ));
  };

  if (isGradient) {
    return (
      <>
        {renderWithLineBreaks(parts[0])}
        <span className="white bg-clip-text text-transparent bg-gradient-to-r from-main-200 to-main-300">
          {renderWithLineBreaks(textToColor)}
        </span>
        {renderWithLineBreaks(parts[1])}
      </>
    );
  }
  return (
    <>
      {renderWithLineBreaks(parts[0])}
      <span className="text-main-100 whitespace-nowrap">
        {renderWithLineBreaks(textToColor)}
      </span>
      {renderWithLineBreaks(parts[1])}
    </>
  );
};
