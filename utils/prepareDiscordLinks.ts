import { DISCORD_CONFIG } from "@/config/discordConfig";

export const prepareDiscordLinkToMessage = (
  messageId: string,
  channelId: string
) => {
  const serverId = DISCORD_CONFIG.NEXT_PUBLIC_DISCORD_SERVER_ID;
  return `https://discord.com/channels/${serverId}/${channelId}/${messageId}`;
};

export const prepareDiscordLinkToChannel = () => {
  const serverId = DISCORD_CONFIG.NEXT_PUBLIC_DISCORD_SERVER_ID;
  const channelId = DISCORD_CONFIG.NEXT_PUBLIC_DISCORD_CHANNEL_ID;
  return `https://discord.com/channels/${serverId}/${channelId}`;
};
