import { MsgExecLegacyContent } from "cosmjs-types/cosmos/gov/v1/tx";
import { Any } from "cosmjs-types/google/protobuf/any";
import { SoftwareUpgradeProposal } from "cosmjs-types/cosmos/upgrade/v1beta1/upgrade";
// 👇 Funkcja do dekodowania
export const decodeProposal = (message: any) => {
  if (message.typeUrl === "/cosmos.gov.v1.MsgExecLegacyContent") {
    // Najpierw dekodujemy MsgExecLegacyContent
    const decodedExec = MsgExecLegacyContent.decode(message.value);

    // Teraz odczytujemy embedded `Any` (czyli faktyczną propozycję)
    const content = decodedExec.content as Any;

    if (content.typeUrl === "/cosmos.upgrade.v1beta1.SoftwareUpgradeProposal") {
      const upgradeProposal = SoftwareUpgradeProposal.decode(content.value);
      return {
        title: upgradeProposal.title,
        description: upgradeProposal.description,
      };
    }

    // Inne typy contentu, np. TextProposal
    // ...
  }

  return null;
};
