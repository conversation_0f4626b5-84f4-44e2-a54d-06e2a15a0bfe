import { chains } from "chain-registry";

/**
 * Pobiera REST API URL dla danego chainName
 * @param chainName - nazwa chainu (np. "empe", "empetestnet", "empedevnet")
 * @returns REST API URL lub undefined jeśli nie znaleziono
 */
export const getRestApiUrl = (chainName: string): string | undefined => {
  const chain = chains.find((c) => c.chain_name === chainName);
  return chain?.apis?.rest?.[0]?.address;
};

/**
 * Pobiera RPC URL dla danego chainName
 * @param chainName - nazwa chainu
 * @returns RPC URL lub undefined jeśli nie znaleziono
 */
export const getRpcUrl = (chainName: string): string | undefined => {
  const chain = chains.find((c) => c.chain_name === chainName);
  return chain?.apis?.rpc?.[0]?.address;
};

/**
 * Pobiera informacje o chainie
 * @param chainName - nazwa chainu
 * @returns obiekt chain lub undefined jeśli nie znaleziono
 */
export const getChainInfo = (chainName: string) => {
  return chains.find((c) => c.chain_name === chainName);
};
