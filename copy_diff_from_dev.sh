#!/bin/bash

# Sprawdzenie, czy branch development istnieje
if ! git rev-parse --verify development &> /dev/null; then
    echo "Branch 'development' nie istnieje. Upewnij się, że masz go lokalnie."
    exit 1
fi

# Lista plików do ignorowania (typowe dla projektów React/React Native/Next.js/Vite/Storybook)
IGNORE_FILES=(
    "yarn.lock"
    "package-lock.json"
    "package.json"
    "pnpm-lock.yaml"
    "node_modules/*"
    ".env"
    ".env.*"
    ".gitignore"
    "tsconfig.json"
    "tsconfig.*.json"
    "babel.config.js"
    "metro.config.js"
    "next.config.js"
    "vite.config.js"
    "jest.config.js"
    "storybook/**/*.json"
    "storybook/**/*.js"
    ".eslint*"
    ".prettier*"
    ".nvmrc"
    ".editorconfig"
    "webpack.config.js"
    "babel.config.cjs"
)

# Pobranie listy plików różniących się względem development
FILES=$(git diff --name-only development)

# Jeśli brak zmian, zakończ skrypt
if [[ -z "$FILES" ]]; then
    echo "Brak plików różniących się względem development."
    exit 0
fi

# Filtrowanie plików – usuwamy te, które są na liście ignorowanych
FILTERED_FILES=()
for file in $FILES; do
    SKIP=false
    for pattern in "${IGNORE_FILES[@]}"; do
        if [[ $file == $pattern ]]; then
            SKIP=true
            break
        fi
    done
    if [[ $SKIP == false ]]; then
        FILTERED_FILES+=("$file")
    fi
done

# Jeśli po przefiltrowaniu nie ma żadnych plików, zakończ skrypt
if [[ ${#FILTERED_FILES[@]} -eq 0 ]]; then
    echo "Brak istotnych plików do skopiowania względem development."
    exit 0
fi

# Przygotowanie zawartości do skopiowania
OUTPUT="Lista plików zmienionych względem 'development':\n\n"

for file in "${FILTERED_FILES[@]}"; do
    if [[ -f "$file" ]]; then  # Sprawdzenie, czy to plik, a nie katalog
        OUTPUT+="--- $file ---\n"
        OUTPUT+="$(cat "$file")\n\n"
    else
        OUTPUT+="--- $file --- (Plik usunięty)\n\n"
    fi
done

# Kopiowanie do schowka w zależności od systemu
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "$OUTPUT" | pbcopy  # macOS
    echo "Lista zmienionych plików została skopiowana do schowka!"
elif command -v xclip &> /dev/null; then
    echo -e "$OUTPUT" | xclip -selection clipboard  # Linux z xclip
    echo "Lista zmienionych plików została skopiowana do schowka!"
elif command -v xsel &> /dev/null; then
    echo -e "$OUTPUT" | xsel --clipboard  # Linux z xsel
    echo "Lista zmienionych plików została skopiowana do schowka!"
else
    echo "Nie znaleziono narzędzia do kopiowania do schowka. Skopiuj ręcznie:"
    echo -e "$OUTPUT"
fi
