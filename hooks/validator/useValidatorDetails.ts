import { useEffect, useState } from "react";
import { useQueryClient } from "../common";

interface Props {
  operatorAddress: string;
}

export const useValidatorDetails = ({ operatorAddress }: Props) => {
  const [validatorDetails, setValidatorDetails] = useState<any>({});
  const [validatorInfo, setValidatorInfo] = useState<any>({
    name: "",
    nick: "",
    avatarUrl: "",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const { graphqlClientRequest, selectedChain } = useQueryClient();

  useEffect(() => {
    const fetchValidatorByOperatorAddress = async () => {
      if (!operatorAddress) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const query = `
            query GetValidatorByOperatorAddress($operatorAddress: String!) {
              validator(where: {validator_info: {operator_address: {_eq: $operatorAddress}}}) {
                validator_info {
                  operator_address
                  validator {
                    validator_descriptions {
                      avatar_url
                      details
                      height
                      identity
                      moniker
                      security_contact
                      validator_address
                      website
                    }
                    validator_commissions {
                      commission
                    }
                    validator_voting_powers {
                      voting_power
                    }
                  }
                }
              }
            }
        `;

        const variables = {
          operatorAddress: operatorAddress,
        };

        const data: any = await graphqlClientRequest.request(query, variables);

        if (data?.validator?.length > 0) {
          setValidatorDetails(data.validator[0]);
          const validatorInfo =
            data.validator[0]?.validator_info?.validator
              .validator_descriptions[0];
          setValidatorInfo({
            name: validatorInfo?.moniker || "",
            nick: validatorInfo?.details || "",
            avatarUrl: validatorInfo?.avatar_url || "",
          });
        }
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching validator details:", error);
        setError(
          error instanceof Error ? error : new Error("Unknown error occurred")
        );
        setIsLoading(false);
      }
    };

    fetchValidatorByOperatorAddress();
  }, [operatorAddress, selectedChain]);

  return { validatorDetails, validatorInfo, isLoading, error };
};
