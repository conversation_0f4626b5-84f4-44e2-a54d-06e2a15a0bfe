import { chainOptions } from "@/config";
import { useChainStore } from "@/store";
import { GraphQLClient } from "graphql-request";
import { createClient } from "graphql-ws";

export const useQueryClient = () => {
  const { selectedChain } = useChainStore();

  const getGraphqlEndpoint = (chainName: string) => {
    const chain = chainOptions.find((chain) => chain.chain_name === chainName);

    if (!chain) {
      const defaultChain = chainOptions[0];
      return {
        ws: defaultChain.addressWSS,
        http: defaultChain.addressHTTP,
      };
    }

    return {
      ws: chain.addressWSS,
      http: chain.addressHTTP,
    };
  };

  const graphqlClient = {
    subscribe: (query: any, sink: any) => {
      const chainName = selectedChain;
      const wsClient = createClient({
        url: getGraphqlEndpoint(chainName).ws,
      });
      return wsClient.subscribe(query, sink);
    },
  };

  const graphqlClientRequest = {
    request: async (query: any, variables?: any) => {
      const chainName = selectedChain;
      const client = new GraphQLClient(getGraphqlEndpoint(chainName).http);
      return client.request(query, variables);
    },
  };

  return { graphqlClient, graphqlClientRequest, selectedChain };
};
