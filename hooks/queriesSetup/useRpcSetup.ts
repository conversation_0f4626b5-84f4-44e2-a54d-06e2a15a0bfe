import { use<PERSON>hain } from "@cosmos-kit/react";
import {
  createRpcQueryHooks,
  useRpcClient,
  useRpcEndpoint,
} from "@empe/empejs";

export const useRpcSetup = (chainName: string | undefined) => {
  const { getRpcEndpoint } = useChain(chainName);

  const rpcEndpointQuery = useRpcEndpoint({
    getter: getRpcEndpoint,
    options: {
      staleTime: Infinity,
      queryKeyHashFn: (queryKey) => {
        return JSON.stringify([...queryKey, chainName]);
      },
    },
  });

  const rpcClientQuery = useRpcClient({
    rpcEndpoint: rpcEndpointQuery.data || "",
    options: {
      enabled: !!rpcEndpointQuery.isSuccess,
      staleTime: Infinity,
    },
  });

  const isDataQueryEnabled =
    !!rpcClientQuery.isSuccess && !!rpcEndpointQuery.isSuccess;

  const { empe: empeQuery, cosmos: cosmosQuery } = createRpcQueryHooks({
    rpc: rpcClientQuery.data,
  });

  return { empeQuery, cosmosQuery, isDataQueryEnabled };
};
