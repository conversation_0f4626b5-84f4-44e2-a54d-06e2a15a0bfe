import { useEffect, useState } from "react";
import { useCon<PERSON><PERSON>hain, useQueryClient } from "./common";

export const useCheckIsValidator = () => {
  const [isValidator, setIsValidator] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { graphqlClientRequest, selectedChain } = useQueryClient();
  const { address, isWalletConnected } = useConnectChain(selectedChain);

  useEffect(() => {
    const fetchIsValidator = async () => {
      setIsLoading(true);
      setError(null);

      if (!isWalletConnected) {
        setIsLoading(false);
        setIsValidator(false);
        return;
      }

      if (!address) {
        setIsLoading(false);
        setIsValidator(false);
        return;
      }

      try {
        const query = `
          query ValidatorByAccount($accountAddress: String!) {
            validator_info(where: {account: {address: {_eq: $accountAddress}}}) {
              operator_address
              account {
                address
              }
            }
          }
        `;

        const variables = {
          accountAddress:
            process.env.NODE_ENV === "production"
              ? address
              : process.env.NEXT_PUBLIC_TESTING_VALIDATOR || "",
        };

        const data: any = await graphqlClientRequest.request(query, variables);

        setIsValidator(!!data?.validator_info?.length);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching validator details:", error);
        setError(
          error instanceof Error ? error : new Error("Unknown error occurred")
        );
        setIsLoading(false);
      }
    };

    fetchIsValidator();
  }, [selectedChain, isWalletConnected, address]);

  return { isValidator, isLoading, error };
};
