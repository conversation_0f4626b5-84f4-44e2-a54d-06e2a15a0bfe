import { createRpcQueryHooks } from "@empe/empejs";

type empeQueryType = ReturnType<typeof createRpcQueryHooks>;

interface Props {
  empeQuery: empeQueryType["empe"];
  cosmosQuery: empeQueryType["cosmos"];
  isDataQueryEnabled: boolean;
}
export const useDashboardQueries = ({
  empeQuery,
  cosmosQuery,
  isDataQueryEnabled,
}: Props) => {
  //INFRACTION QUERY
  const inflationQuery = empeQuery.cfeminter.useInflation({
    options: {
      enabled: isDataQueryEnabled,
      select: (data) => ({
        inflation: data.inflation,
      }),
    },
  });

  const totalSupplyQuery = cosmosQuery.bank.v1beta1.useTotalSupply({
    options: {
      enabled: isDataQueryEnabled,
    },
  });

  const didDocumentCount = empeQuery.diddoc.useDidDocumentAll({
    options: {
      enabled: isDataQueryEnabled,
      select: (data) => ({
        count: Number(data.pagination?.total || 0),
        didDocument: data.didDocument,
      }),
    },
  });

  const communityPoolQuery = cosmosQuery.distribution.v1beta1.useCommunityPool({
    options: {
      enabled: isDataQueryEnabled,
    },
  });

  const bondedTokensQuery = cosmosQuery.staking.v1beta1.usePool({
    options: {
      enabled: isDataQueryEnabled,
    },
  });

  const tokenBurnedQuery = cosmosQuery.bank.v1beta1.useBalance({
    request: {
      address: "empe1sk06e3dyexuq4shw77y3dsv480xv42mqgnr2ap",
      denom: "uempe",
    },
    options: {
      enabled: isDataQueryEnabled,
    },
  });

  //ALL QUERIES
  const allQueries = {
    inflation: inflationQuery,
    totalSupply: totalSupplyQuery,
    didDocumentCount: didDocumentCount,
    communityPool: communityPoolQuery,
    bondedTokens: bondedTokensQuery,
    tokenBurned: tokenBurnedQuery,
  };

  const queriesWithUnchangingKeys: any[] = [
    allQueries.inflation,
    allQueries.totalSupply,
    allQueries.didDocumentCount,
    allQueries.communityPool,
    allQueries.bondedTokens,
    allQueries.tokenBurned,
  ];

  const updatableQueriesAfterMutation: any[] = [];

  return {
    allQueries,
    queriesWithUnchangingKeys,
    updatableQueriesAfterMutation,
  };
};
