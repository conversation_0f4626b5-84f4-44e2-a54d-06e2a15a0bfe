import { useEffect, useState } from "react";
import { useQueryClient } from "../common";

interface MessageResponse {
  data: {
    message: {
      transaction: {
        messages: Array<{
          "@type": string;
          sender: string;
          signatures: Array<{
            methodId: string;
            sigBytes: string;
          }>;
        }>;
      };
    }[];
  };
}

export const useTransactions = () => {
  const [transactions, setTransactions] = useState([]);
  const { graphqlClient, graphqlClientRequest, selectedChain } =
    useQueryClient();

  useEffect(() => {
    const unsubscribeTransactions = graphqlClient.subscribe(
      {
        query: `
          subscription TransactionsListener($limit: Int = 7, $offset: Int = 0) {
            transactions: transaction(limit: $limit, offset: $offset, order_by: {height: desc}) {
              hash
              block {
                timestamp
              }
              messages
            }
          }
        `,
        variables: { limit: 7, offset: 0 },
      },
      {
        next: (data: any) => {
          setTransactions(data.data.transactions);
        },
        error: (err: any) => console.error("Subscription error:", err),
        complete: () => console.log("Subscription complete"),
      }
    );

    return () => {
      unsubscribeTransactions();
    };
  }, [selected<PERSON>hain]);

  return { transactions };
};
