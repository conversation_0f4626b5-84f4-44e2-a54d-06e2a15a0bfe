import { useState, useEffect } from "react";
import { useQueryClient } from "../common";

export const useBlocks = () => {
  const [blocks, setBlocks] = useState([]);
  const { graphqlClient, selectedChain } = useQueryClient();

  useEffect(() => {
    const unsubscribe = graphqlClient.subscribe(
      {
        query: `
          subscription BlocksListener($limit: Int = 7, $offset: Int = 0) {
            blocks: block(limit: $limit, offset: $offset, order_by: {height: desc}) {
              txs: num_txs
              hash
              height
              timestamp
              validator {
                validatorInfo: validator_info {
                  operatorAddress: operator_address
                    validator {
                      validator_descriptions {
                        moniker
                        avatar_url
                      }
                  }
                }
              }
            }
          }
        `,
        variables: { limit: 7, offset: 0 },
      },
      {
        next: (data: any) => {
          setBlocks(data.data.blocks);
        },
        error: (err: any) => console.error("Subscription error:", err),
        complete: () => console.log("Subscription complete"),
      }
    );

    return () => {
      unsubscribe();
    };
  }, [selected<PERSON>hai<PERSON>]);

  return { blocks };
};
