import { useChainStore } from "@/store";
import { Explorer } from "@chain-registry/types/chain.schema";
import { use<PERSON>hain } from "@cosmos-kit/react";
import { useEffect, useState } from "react";

export const useExplorerUrl = () => {
  const { selectedChain } = useChainStore();
  const [explorer, setExplorer] = useState<Explorer | null>(null);
  const { chain } = useChain(selectedChain);

  const getExplorerUrl = () => {
    const selectedExplorer = chain.explorers[0];

    if (!selectedExplorer) return;

    return {
      url: selectedExplorer.url,
      tx_page: selectedExplorer.tx_page,
      block_page: selectedExplorer.block_page,
    };
  };

  useEffect(() => {
    if (chain) {
      const selectedExplorer = getExplorerUrl();
      setExplorer(selectedExplorer);
    }
  }, [selectedChain, chain]);

  return { explorer };
};
