import { DashboardSummaryInfoTypes } from "@/enums/dashboardSummaryInfoTypes";
import { useDashboardStore } from "@/store/dashboardStore";

import { useEffect, useState } from "react";
import { useQueryClient } from "../common";

const TRANSACTION_COUNT_QUERY = `
subscription TransactionCountListener {
  transaction_aggregate {
    aggregate {
      count
    }
  }
}
`;

const WALLET_COUNT_QUERY = `
subscription WalletCountListener {
  top_accounts_aggregate {
    aggregate {
      count
    }
  }
}
`;

const DID_COUNT_QUERY = `
subscription DidCountListener {
  did_document_aggregate {
    aggregate {
      count
    }
  }
}
`;

const PROTOCOL_REVENUE_QUERY = `
subscription MyQuery {
  fees_aggregate {
    aggregate {
      sum {
        fee_value
        stable_fee_value
      }
    }
  }
}
`;

export const useSummaryCount = () => {
  const { tokenBurned } = useDashboardStore();
  const { graphqlClient, selectedChain } = useQueryClient();
  const [summaryCount, setSummaryCount] = useState<{
    [key in DashboardSummaryInfoTypes]: number;
  }>({
    [DashboardSummaryInfoTypes.TotalDids]: 0,
    [DashboardSummaryInfoTypes.TotalWallets]: 0,
    [DashboardSummaryInfoTypes.TotalTransactions]: 0,
    [DashboardSummaryInfoTypes.TotalBurnedCoins]: 0,
    [DashboardSummaryInfoTypes.ProtocolRevenue]: 0,
  });

  useEffect(() => {
    const subscriptions = [
      graphqlClient.subscribe(
        { query: TRANSACTION_COUNT_QUERY },
        {
          next: (data: any) => {
            setSummaryCount((prev) => ({
              ...prev,
              [DashboardSummaryInfoTypes.TotalTransactions]:
                data.data.transaction_aggregate.aggregate.count,
            }));
          },
          error: (error: any) =>
            console.error("Transaction subscription error:", error),
          complete: () =>
            console.log("Transaction count subscription completed"),
        }
      ),
      graphqlClient.subscribe(
        { query: WALLET_COUNT_QUERY },
        {
          next: (data: any) => {
            setSummaryCount((prev) => ({
              ...prev,
              [DashboardSummaryInfoTypes.TotalWallets]:
                data.data.top_accounts_aggregate.aggregate.count,
            }));
          },
          error: (error: any) =>
            console.error("Wallet subscription error:", error),
          complete: () => console.log("Wallet count subscription completed"),
        }
      ),
      graphqlClient.subscribe(
        { query: DID_COUNT_QUERY },
        {
          next: (data: any) => {
            setSummaryCount((prev) => ({
              ...prev,
              [DashboardSummaryInfoTypes.TotalDids]:
                data.data.did_document_aggregate.aggregate.count,
            }));
          },
          error: (error: any) =>
            console.error("DID subscription error:", error),
          complete: () => console.log("DID count subscription completed"),
        }
      ),
      graphqlClient.subscribe(
        { query: PROTOCOL_REVENUE_QUERY },
        {
          next: (data: any) => {
            setSummaryCount((prev) => ({
              ...prev,
              [DashboardSummaryInfoTypes.ProtocolRevenue]:
                data.data.fees_aggregate.aggregate.sum.fee_value,
            }));
          },
          error: (error: any) =>
            console.error("Protocol revenue subscription error:", error),
          complete: () =>
            console.log("Protocol revenue subscription completed"),
        }
      ),
    ];

    return () => subscriptions.forEach((unsubscribe) => unsubscribe());
  }, [selectedChain]);

  return {
    summaryCount: {
      ...summaryCount,
      [DashboardSummaryInfoTypes.TotalBurnedCoins]: tokenBurned,
    },
  };
};
