import { useChainStore } from "@/store";
import { useDashboardStore } from "@/store/dashboardStore";
import { useDashboardData } from "./useDashboardData";
import { useEffect } from "react";

export const useDashboard = () => {
  const { selectedChain } = useChainStore();
  const dashboardStore = useDashboardStore();
  const { data, isLoading, fetchAllData } = useDashboardData(selectedChain);

  // Aktualizuj store tylko raz, gdy dane są dostępne
  useEffect(() => {
    if (data) {
      // U<PERSON>ywamy setTimeout, aby przer<PERSON>ć cykl renderowania
      setTimeout(() => {
        dashboardStore.setAllData(data);
        dashboardStore.setIsLoading(isLoading);
      }, 0);
    } else {
      // Jeśli dane nie są dostępne, ustaw tylko stan ładowania
      dashboardStore.setIsLoading(true);
    }
  }, [data, isLoading]);

  // Expose the ability to manually refresh data
  const refreshDashboardData = () => {
    dashboardStore.setIsLoading(true);
    fetchAllData();
  };

  return {
    ...dashboardStore,
    refreshDashboardData
  };
};
