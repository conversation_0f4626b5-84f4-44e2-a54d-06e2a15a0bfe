import { useEffect, useState } from "react";
import { useQueryClient } from "../common";
import { StakingUptime } from "@/types/stakingUptime";

export const useUptime = () => {
  const [uptime, setUptime] = useState<StakingUptime>({});
  const { graphqlClientRequest, selected<PERSON>hain } = useQueryClient();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const queryMissedBlocks = `
          query MyQuery {
            validator_signing_info {
              validator_address
              missed_blocks_counter
            }
            validator_info {
              consensus_address
              operator_address
            }
          }
        `;

        const queryTotalBlocks = `
          query MyQuery {
            slashing_params {
              params
            }
          }
        `;

        const [dataMissedBlocks, dataTotalBlocks]: any = await Promise.all([
          graphqlClientRequest.request(queryMissedBlocks),
          graphqlClientRequest.request(queryTotalBlocks),
        ]);

        const validatorsUptimes = dataMissedBlocks.validator_signing_info
          .map((si: any) => ({
            validator_address: si.validator_address,
            missed_blocks_counter: si.missed_blocks_counter,
            operator_address:
              dataMissedBlocks.validator_info.find(
                (vi: any) => vi.consensus_address === si.validator_address
              )?.operator_address || null,
          }))
          .reduce((acc: StakingUptime, item: any) => {
            if (!item.operator_address) return acc;

            const missedBlocks = item.missed_blocks_counter || 0;
            const window =
              dataTotalBlocks.slashing_params[0]?.params
                ?.signed_blocks_window || 0;

            let uptime = 0;
            if (window > 0) {
              uptime = ((window - missedBlocks) / window) * 100;
              uptime = Number(uptime.toFixed(2));
            }

            acc[item.operator_address] = {
              uptime: uptime,
            };

            return acc;
          }, {});

        setUptime(validatorsUptimes);
      } catch (error) {
        console.error("Error fetching validator logos:", error);
      }
    };

    fetchData();
  }, [selectedChain]);

  return { uptime };
};
