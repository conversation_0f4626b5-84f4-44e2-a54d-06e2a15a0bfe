import { useEffect, useState } from "react";
import { useQueryClient } from "../common";
import { ValidatorLogos } from "@/types/validatorLogos";

export const useValidatorLogos = () => {
  const [logos, setLogos] = useState<ValidatorLogos>({});
  const { graphqlClientRequest, selectedChain } = useQueryClient();

  useEffect(() => {
    const fetchLogos = async () => {
      try {
        const query = `
          query GetValidatorDescriptions {
            validator_description {
              validator_address
              avatar_url
              validator {
                validator_info {
                  operator_address
                }
              }
            }
          }
        `;

        const data: any = await graphqlClientRequest.request(query);

        if (data && data.validator_description) {
          const logosMap: ValidatorLogos = data.validator_description.reduce(
            (acc: { [key: string]: string }, item: any) => {
              if (
                item.validator.validator_info.operator_address &&
                item.avatar_url
              ) {
                acc[item.validator.validator_info.operator_address] =
                  item.avatar_url;
              }
              return acc;
            },
            {}
          );
          setLogos(logosMap);
        }
      } catch (error) {
        console.error("Error fetching validator logos:", error);
      }
    };

    fetchLogos();
  }, [selectedChain]);

  return { logos };
};
