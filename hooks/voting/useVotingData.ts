import { useEffect, useMemo, useState } from "react";
import { use<PERSON>hai<PERSON> } from "@cosmos-kit/react";
import { useQueries, useQuery } from "@tanstack/react-query";
import { ProposalStatus } from "interchain-query/cosmos/gov/v1/gov";
import { Proposal as ProposalV1 } from "interchain-query/cosmos/gov/v1/gov";
import { useQueryHooks, useRpcQueryClient } from ".";
import { getTitle, paginate, parseQuorum } from "@/utils";
import { chains } from "chain-registry";
import { handleGetProposals } from "@/api/getProposals";
import { getRestApiUrl } from "@/utils/chain";

(BigInt.prototype as any).toJSON = function () {
  return this.toString();
};

export interface Votes {
  [key: string]: number;
}

/**
 * Transformuje dane z REST API do formatu oczekiwanego przez komponenty
 */
function transformRestProposalToRpcFormat(restProposal: any): ProposalV1 {
  return {
    ...restProposal,
    // Konwersja dat ze stringów na obiekty Date
    submitTime: restProposal.submit_time
      ? new Date(restProposal.submit_time)
      : undefined,
    depositEndTime: restProposal.deposit_end_time
      ? new Date(restProposal.deposit_end_time)
      : undefined,
    votingStartTime: restProposal.voting_start_time
      ? new Date(restProposal.voting_start_time)
      : undefined,
    votingEndTime: restProposal.voting_end_time
      ? new Date(restProposal.voting_end_time)
      : undefined,

    // Mapowanie pól z snake_case na camelCase
    finalTallyResult: restProposal.final_tally_result
      ? {
          yesCount: restProposal.final_tally_result.yes_count,
          noCount: restProposal.final_tally_result.no_count,
          abstainCount: restProposal.final_tally_result.abstain_count,
          noWithVetoCount: restProposal.final_tally_result.no_with_veto_count,
        }
      : undefined,

    totalDeposit: restProposal.total_deposit,

    // Zachowanie oryginalnych pól dla kompatybilności
    submit_time: restProposal.submit_time,
    deposit_end_time: restProposal.deposit_end_time,
    voting_start_time: restProposal.voting_start_time,
    voting_end_time: restProposal.voting_end_time,
    final_tally_result: restProposal.final_tally_result,
    total_deposit: restProposal.total_deposit,
  } as ProposalV1;
}

export function processProposals(proposals: ProposalV1[]) {
  const sorted = proposals.sort((a, b) => Number(b.id) - Number(a.id));

  proposals.forEach((proposal) => {
    // @ts-ignore
    if (!proposal.content?.title && proposal.content?.value) {
      // @ts-ignore
      proposal.content.title = getTitle(proposal.content?.value);
    }
  });

  return sorted
    .filter(
      ({ status }) => status === ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD
    )
    .concat(
      sorted.filter(
        ({ status }) => status !== ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD
      )
    );
}

export function useVotingData(chainName: string) {
  const [isLoading, setIsLoading] = useState(false);
  const { address } = useChain(chainName);
  const { rpcQueryClient } = useRpcQueryClient(chainName);
  const { cosmos, isReady, isFetching } = useQueryHooks(chainName);
  const chain = chains.find((c) => c.chain_name === chainName);

  // Zastąpienie RPC query na REST API query
  const restApiUrl = getRestApiUrl(chainName);

  const proposalsQuery = useQuery({
    queryKey: ["proposals", chainName, restApiUrl],
    queryFn: async () => {
      if (!restApiUrl) {
        console.error(`REST API URL not found for chain: ${chainName}`);
        throw new Error(`REST API URL not found for chain: ${chainName}`);
      }

      console.log(`Fetching proposals from REST API: ${restApiUrl}`);
      const response = await handleGetProposals({ restApiUrl });

      if (!response.proposals) {
        console.warn(`No proposals found in response from ${restApiUrl}`);
        return [];
      }

      console.log(
        `Successfully fetched ${response.proposals.length} proposals from REST API`
      );

      // Transformuj dane z REST API do formatu RPC
      const transformedProposals = response.proposals.map(
        transformRestProposalToRpcFormat
      );

      console.log(
        `Transformed ${transformedProposals.length} proposals:`,
        transformedProposals.map((p: ProposalV1) => ({
          id: p.id,
          status: p.status,
          votingEndTime: p.votingEndTime,
          title: p.title,
        }))
      );

      const processedProposals = processProposals(transformedProposals);
      console.log(
        `Processed ${processedProposals.length} proposals after filtering`
      );

      return processedProposals;
    },
    enabled: Boolean(restApiUrl),
    staleTime: Infinity,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const bondedTokensQuery = cosmos.staking.v1beta1.usePool({
    options: {
      enabled: isReady,
      staleTime: Infinity,
      select: ({ pool }) => pool?.bondedTokens,
    },
  });

  const quorumQuery = cosmos.gov.v1.useParams({
    request: { paramsType: "tallying" },
    options: {
      enabled: isReady,
      staleTime: Infinity,
      select: ({ tallyParams }) => parseQuorum(tallyParams?.quorum as any),
    },
  });

  const votedProposalsQuery = cosmos.gov.v1.useProposals({
    request: {
      voter: address || "/", // use '/' to differentiate from proposalsQuery
      depositor: "",
      pagination: paginate(50n, true),
      proposalStatus: ProposalStatus.PROPOSAL_STATUS_UNSPECIFIED,
    },
    options: {
      enabled: isReady && Boolean(address),
      select: ({ proposals }) => proposals,
      keepPreviousData: true,
    },
  });

  const votesQueries = useQueries({
    queries: (votedProposalsQuery.data || []).map(({ id }: { id: bigint }) => ({
      queryKey: ["voteQuery", id, address],
      queryFn: () =>
        rpcQueryClient?.cosmos.gov.v1.vote({
          proposalId: id,
          voter: address || "",
        }),
      enabled:
        Boolean(rpcQueryClient) &&
        Boolean(address) &&
        Boolean(votedProposalsQuery.data),
      keepPreviousData: true,
    })),
  });

  const singleQueries = {
    quorum: quorumQuery,
    proposals: proposalsQuery,
    bondedTokens: bondedTokensQuery,
    votedProposals: votedProposalsQuery,
  };

  const staticQueries = [
    singleQueries.quorum,
    singleQueries.proposals,
    singleQueries.bondedTokens,
  ];

  const dynamicQueries = [singleQueries.votedProposals];

  useEffect(() => {
    staticQueries.forEach((query) => query.remove());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chainName]);

  const isStaticQueriesFetching = staticQueries.some(
    ({ isFetching }) => isFetching
  );

  const isDynamicQueriesFetching =
    singleQueries.votedProposals.isFetching ||
    votesQueries.some(({ isFetching }) => isFetching);

  const loading =
    isFetching || isStaticQueriesFetching || isDynamicQueriesFetching;

  useEffect(() => {
    // no loading when refetching
    if (isFetching || isStaticQueriesFetching) setIsLoading(true);
    if (!loading) setIsLoading(false);
  }, [isStaticQueriesFetching, loading]);

  type SingleQueries = typeof singleQueries;

  type SingleQueriesData = {
    [Key in keyof SingleQueries]: NonNullable<SingleQueries[Key]["data"]>;
  };

  const singleQueriesData = useMemo(() => {
    if (isStaticQueriesFetching || (!isReady && !restApiUrl)) return;

    const singleQueriesData = Object.fromEntries(
      Object.entries(singleQueries).map(([key, query]) => [key, query.data])
    ) as SingleQueriesData;

    singleQueriesData?.proposals.forEach((proposal: ProposalV1) => {
      if (proposal.status === ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD) {
        (async () => {
          for (const { address } of chain?.apis?.rest || []) {
            const api = `${address}/cosmos/gov/v1/proposals/${Number(
              proposal.id
            )}/tally`;
            try {
              const tally = (await (await fetch(api)).json()).tally;
              if (!tally) {
                continue;
              }
              proposal.finalTallyResult = {
                yesCount: tally.yes_count,
                noCount: tally.no_count,
                abstainCount: tally.abstain_count,
                noWithVetoCount: tally.no_with_veto_count,
              };
              break;
            } catch (e) {
              console.error("error fetch tally", api);
            }
          }
        })();
      }
    });

    return singleQueriesData;
  }, [isStaticQueriesFetching, isReady, restApiUrl]);

  const votes = useMemo(() => {
    const votesEntries = votesQueries
      .map((query) => query.data)
      .map((data: any) => [
        data?.vote?.proposalId,
        data?.vote?.options[0].option,
      ]);

    return Object.fromEntries(votesEntries) as Votes;
  }, [votesQueries]);

  const refetch = () => {
    votesQueries.forEach((query) => query.remove());
    dynamicQueries.forEach((query) => query.refetch());
  };

  return { data: { ...singleQueriesData, votes }, isLoading, refetch };
}
