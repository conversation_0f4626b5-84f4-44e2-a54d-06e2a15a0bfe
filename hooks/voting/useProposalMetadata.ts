import { useEffect, useState } from "react";
import { Proposal as IProposal } from "interchain-query/cosmos/gov/v1/gov";

interface Props {
  proposal: IProposal; // Replace with the actual type of your proposal object
}

export const useProposalMetadata = ({ proposal }: Props) => {
  const [proposalTitle, setProposalTitle] = useState<string>("Loading...");
  const [proposalDescription, setProposalDescription] = useState<string>("");

  useEffect(() => {
    try {
      if (proposal.metadata) {
        const metadata = JSON.parse(proposal.metadata);
        if (metadata?.description) {
          setProposalDescription(metadata.description);
        }
        if (metadata?.title) {
          setProposalTitle(metadata.title);
          return;
        }
      }

      if (proposal.messages?.[0]?.typeUrl) {
        const msgType = proposal.messages[0].typeUrl.split(".").pop() || "";
        const readableTitle = msgType
          .replace(/^Msg/, "")
          .split(/(?=[A-Z])/)
          .join(" ")
          .trim();

        setProposalTitle(`${readableTitle} - Proposal #${proposal.id}`);
      } else {
        setProposalTitle(`Proposal #${proposal.id}`);
      }
    } catch (e) {
      setProposalTitle(`Proposal #${proposal.id}`);
    }
  }, [proposal]);

  return {
    proposalTitle,
    proposalDescription,
  };
};
