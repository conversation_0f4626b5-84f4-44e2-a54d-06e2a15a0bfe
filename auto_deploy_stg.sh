#!/bin/bash

export KUBECONFIG=~/.kube/config_mainnet

# Define variables
DEPLOYMENT_CATALOG="./deployment"
DEPLOYMENT_FILE="$DEPLOYMENT_CATALOG/empe-hub-deployment.yaml"
IMAGE_PREFIX="309596z9.c1.gra9.container-registry.ovh.net/empe/services/empehub:"

# Debugging step: Show the current image line in the deployment file
echo "Current image line in deployment file:"
grep "image:" "$DEPLOYMENT_FILE"

# Use grep to extract the current image tag
IMAGE_TAG=$(grep -oE '309596z9\.c1\.gra9\.container-registry\.ovh\.net/empe/services/empehub:[0-9]+\.[0-9]+\.[0-9]+' "$DEPLOYMENT_FILE" | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')

if [[ -z "$IMAGE_TAG" ]]; then
  echo "Error: Unable to extract the current image tag from the deployment file."
  exit 1
fi

# Bump the version (increment the patch version)
IFS='.' read -r major minor patch <<< "$IMAGE_TAG"
new_patch=$((patch + 1))
NEW_IMAGE_TAG="$major.$minor.$new_patch"
NEW_IMAGE="$IMAGE_PREFIX$NEW_IMAGE_TAG"

echo "New image name: $NEW_IMAGE"

# Update the image tag in the deployment file
sed -i '' "s|image: .*|image: $NEW_IMAGE|g" "$DEPLOYMENT_FILE"

# Build the Next.js app locally before Docker build
echo "Building Next.js application..."
yarn build

docker build --platform linux/amd64  -t $NEW_IMAGE -f ./Dockerfile .
#docker build --platform linux/amd64  -t $NEW_IMAGE -f ./empe-hub/Dockerfile .
#docker build --platform linux/amd64 -t empe-hub -f ./empe-hub/Dockerfile .

docker push $NEW_IMAGE

# Apply the updated deployment
echo $DEPLOYMENT_CATALOG
kubectl apply -f "$DEPLOYMENT_CATALOG/" --validate=false
# Git commit and push changes

DEPLOYMENT_LOCAL_CATALOG="./deployment"
DEPLOYMENT__LOCAL_FILE="$DEPLOYMENT_LOCAL_CATALOG/empe-hub-deployment.yaml"

# git add "$DEPLOYMENT__LOCAL_FILE"
# git commit -m "Bumped image tag to $NEW_IMAGE_TAG"
# git push origin development