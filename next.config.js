/** @type {import('next').NextConfig} */

module.exports = {
  output: "standalone",
  distDir: "dist",
  reactStrictMode: false,
  swcMinify: true,
  webpack: (config) => {
    config.module.rules.push({
      test: /\.yaml$/,
      use: "yaml-loader",
    });

    return config;
  },
  images: {
    domains: [
      "raw.githubusercontent.com",
      "s3.amazonaws.com",
      "cdn.discordapp.com",
    ],
  },
};
