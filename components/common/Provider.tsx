import { ThemeProvider, useTheme } from "@interchain-ui/react";
import { lightTheme } from "@/config";

type CustomThemeProviderProps = {
  children: React.ReactNode;
};

export const CustomThemeProvider = ({ children }: CustomThemeProviderProps) => {
  const { theme } = useTheme();

  return (
    <ThemeProvider
      defaultTheme="light"
      themeDefs={[lightTheme]}
      customTheme={theme}
    >
      {children}
    </ThemeProvider>
  );
};
