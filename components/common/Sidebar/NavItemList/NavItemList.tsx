import { useCheckIsValidator } from "@/hooks/useCheckIsValidator";
import { NavItem } from "../NavItem/index";

const navItems = [
  { icon: "home", label: "Home", href: "/" },
  { icon: "node", label: "Nodes", href: "/nodes" },
  { icon: "governance", label: "Governance", href: "/governance" },
  { icon: "staking", label: "Staking", href: "/staking" },
  { icon: "airdrop", label: "Airdrop", href: "/airdrop" },
];

interface NavItemsProps {
  onItemClick: () => void;
}

export const NavItemList = ({ onItemClick }: NavItemsProps) => {
  const { isValidator } = useCheckIsValidator();

  const activeNavItems = isValidator
    ? [
        ...navItems,
        {
          icon: "validator",
          label: "Validator",
          href: "/validator",
        },
      ]
    : navItems;

  return (
    <div className="flex flex-col justify-start items-start text-left gap-8">
      {activeNavItems.map(({ href, icon, label }) => (
        <NavItem
          key={label}
          icon={icon}
          label={label}
          href={href}
          onClick={onItemClick}
        />
      ))}
    </div>
  );
};
