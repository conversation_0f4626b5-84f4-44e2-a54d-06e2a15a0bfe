import Link from "next/link";
import { useRouter } from "next/router";
import Image from "next/image";
import clsx from "clsx";

const iconMap: Record<
  "home" | "staking" | "airdrop" | "governance" | "node" | "validator",
  { default: string; active: string }
> = {
  home: {
    default: "/assets/home_icon.svg",
    active: "/assets/home_icon_blue.svg",
  },
  staking: {
    default: "/assets/staking_icon.svg",
    active: "/assets/staking_icon_blue.svg",
  },
  airdrop: {
    default: "/assets/airdrop_icon.svg",
    active: "/assets/airdrop_icon_blue.svg",
  },
  governance: {
    default: "/assets/governance_icon.svg",
    active: "/assets/governance_icon_blue.svg",
  },
  node: {
    default: "/assets/node_icon.svg",
    active: "/assets/node_icon_blue.svg",
  },
  validator: {
    default: "/assets/validator_icon_menu.svg",
    active: "/assets/validator_icon_menu_blue.svg",
  },
};

interface Props {
  icon: string;
  label: string;
  href: string;
  onClick: () => void;
}

export const NavItem = ({ icon, label, href, onClick }: Props) => {
  const router = useRouter();
  const isActive = router.pathname === href;
  const iconSrc = isActive
    ? iconMap[icon as keyof typeof iconMap].active
    : iconMap[icon as keyof typeof iconMap].default;

  return (
    <Link href={href}>
      <div
        className={clsx(
          "p-2.5 flex items-center gap-6 h-10 cursor-pointer rounded-md",
          isActive ? "text-main-100" : "text-main-600"
        )}
        onClick={onClick}
      >
        <Image src={iconSrc} alt={label} width={20} height={20} />
        <span
          className={clsx(
            "text-md font-medium",
            isActive ? "text-main-100" : "text-main-600"
          )}
        >
          {label}
        </span>
      </div>
    </Link>
  );
};
