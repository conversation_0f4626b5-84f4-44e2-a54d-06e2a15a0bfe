import Image from "next/image";
import Link from "next/link";
import { VscClose } from "react-icons/vsc";
import { Drawer } from "@/components";
import { useDetectBreakpoints } from "@/hooks";
import { SidebarContent } from "./SidebarContent";
import { LinkGradient } from "@empe/front-kit-next-ui";

type SidebarProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const Sidebar = ({ isOpen, onClose }: SidebarProps) => {
  const { isMobile } = useDetectBreakpoints();

  const showMobileMenu = isMobile;

  const renderExternalLinks = () => {
    return (
      <div className="absolute bottom-0 flex flex-col gap-6 left-0 right-0 p-4">
        <LinkGradient
          target="_blank"
          onClick={onClose}
          href="https://docs.empe.io/airdrop/faucet-guide-how-to-claim-testnet-tokens"
          style={{ fontSize: "0.8rem" }}
        >
          How to Claim Testnet Tokens
        </LinkGradient>
        <Link
          onClick={onClose}
          href="/terms-and-conditions"
          className="text-main-600/60 text-center flex justify-center items-center gap-2"
        >
          <Image src={"/assets/terms.svg"} alt="" width={12} height={12} />
          Terms & Conditions
        </Link>
      </div>
    );
  };

  const desktopSidebar = (
    <div className="text-main-600 bg-main-600/10 flex items-start flex-col relative gap-2 max-h-full">
      <Link href="/" style={{ marginBottom: "50px" }}>
        <div className="px-10 pt-10">
          <Image
            src={"/assets/navLogo.png"}
            alt="empeiria logo"
            width="300"
            height="100"
            style={{ width: "180px", height: "auto" }}
          />
        </div>
      </Link>
      <div className="px-8">
        <SidebarContent onClose={onClose} />
      </div>
      {renderExternalLinks()}
    </div>
  );

  const mobileSidebar = (
    <Drawer isOpen={isOpen} onClose={onClose} direction="right">
      <div className="text-main-600 bg-main-500/90 h-full min-h-[650px] overflow-y-auto relative w-[320px] px-[20px] py-[30px] flex flex-col">
        <div className="flex justify-between mb-[64px] mr-[10px] text-blackAlpha-400">
          <Link href="/" style={{ marginBottom: "50px" }}>
            <Image
              src={"/assets/navLogo.png"}
              alt="empeiria logo"
              width="300"
              height="100"
              style={{ width: "180px", height: "auto" }}
            />
          </Link>
          <VscClose size="26px" cursor="pointer" onClick={onClose} />
        </div>
        <SidebarContent onClose={onClose} />
        {renderExternalLinks()}
      </div>
    </Drawer>
  );

  return showMobileMenu ? mobileSidebar : desktopSidebar;
};
