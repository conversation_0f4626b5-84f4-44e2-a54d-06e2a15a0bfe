import Link from "next/link";
import { FaXTwitter } from "react-icons/fa6";
import { Icon } from "@interchain-ui/react";
import clsx from "clsx";

import { useDetectBreakpoints } from "@/hooks";

export const Footer = () => {
  const { isMobile } = useDetectBreakpoints();

  return (
    <div className="mt-8">
      {isMobile && (
        <div className="flex justify-center mb-2.5">
          <SocialLinks />
        </div>
      )}
      <div
        className={clsx(
          "flex items-center gap-1",
          isMobile ? "justify-center" : "justify-between"
        )}
      >
        <span className="text-blackAlpha500 text-xs font-medium">
          © {new Date().getFullYear()} Empeiria
        </span>
        {isMobile ? <TextDivider /> : <SocialLinks />}
        <Link href="/terms-and-conditions">
          <span className="text-blackAlpha500 text-xs font-medium">
            Terms of Service
          </span>
        </Link>
      </div>
    </div>
  );
};

const TextDivider = () => {
  return <span className="text-main-600 text-xs font-medium">|</span>;
};

const socialLinks = [
  {
    icon: <Icon name="github" color="#fff" />,
    href: "https://github.com/empe-io",
  },
  {
    icon: <Icon name="discord" color="#fff" />,
    href: "https://discord.com/invite/BbCzfuJzkv",
  },
  {
    icon: (
      <div className="#fff">
        <FaXTwitter size="16px" />
      </div>
    ),
    href: "https://x.com/empe_io",
  },
  {
    icon: <Icon name="youtube" color="#fff" />,
    href: "https://www.youtube.com/@Empeiria_web3",
  },
];

const SocialLinks = () => {
  return (
    <div className="flex items-center gap-4">
      {socialLinks.map(({ icon, href }) => (
        <Link href={href} target="_blank" key={href}>
          {icon}
        </Link>
      ))}
    </div>
  );
};
