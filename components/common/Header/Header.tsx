import { useConnectChain, useCopyToClipboard } from "@/hooks";
import { useChainStore } from "@/store";
import { ChainDropdown } from "./ChainDropdown";
import { ConnectToWallet } from "./ConnectToWallet";

interface Props {
  isOpen: () => void;
}

export const Header = ({ isOpen }: Props) => {
  const { selectedChain } = useChainStore();
  const { isCopied, copyToClipboard } = useCopyToClipboard();
  const { connect, disconnect, address, isWalletConnected, wallet } =
    useConnectChain(selectedChain);

  return (
    <div className="flex flex-row gap-4 py-8 w-full justify-between lg:justify-end items-start">
      <div className="flex flex-col-reverse w-full lg:flex-row gap-4 md:gap-6 items-start lg:items-end justify-end">
        <div className="w-full md:max-w-[200px]">
          <ChainDropdown />
        </div>
        <div className="w-full md:max-w-[200px]">
          <ConnectToWallet
            wallet={wallet}
            address={address}
            isWalletConnected={isWalletConnected}
            connect={connect}
            disconnect={disconnect}
            copyToClipboard={copyToClipboard}
            isCopied={isCopied}
          />
        </div>
      </div>
    </div>
  );
};
