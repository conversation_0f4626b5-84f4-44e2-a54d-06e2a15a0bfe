"use client";

import Image from "next/image";
import { FiLogOut } from "react-icons/fi";
import { useState } from "react";
import { <PERSON>C<PERSON>, FiCheck } from "react-icons/fi";

interface WalletType {
  logo?: string | { major?: string; minor?: string };
  prettyName: string;
}

interface Props {
  wallet?: WalletType;
  address?: string;
  isWalletConnected: boolean;
  connect: () => void;
  disconnect: () => void;
  copyToClipboard: (text: string) => void;
  isCopied: boolean;
}

export const ConnectToWallet = ({
  wallet,
  address,
  isWalletConnected,
  connect,
  disconnect,
  copyToClipboard,
}: Props) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    if (address) {
      copyToClipboard(address);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const shortenAddress = (addr: string, length: number = 6) => {
    return addr ? `${addr.slice(0, length)}...${addr.slice(-length)}` : "";
  };

  return (
    <div className="flex flex-row min-w-[210px] w-full">
      {isWalletConnected && address ? (
        <div className="flex items-center space-x-2 border w-full border-white/20 rounded-lg overflow-hidden">
          <button
            className="flex items-center px-4 py-2 gap-0 border-r w-full border-white/20"
            onClick={handleCopy}
          >
            {wallet?.logo ? (
              <Image
                src={
                  typeof wallet.logo === "string"
                    ? wallet.logo
                    : wallet.logo.major || wallet.logo.minor || ""
                }
                alt={wallet.prettyName}
                width={20}
                height={20}
                className="mr-2"
              />
            ) : (
              <span className="mr-2">🔗</span>
            )}
            {shortenAddress(address, 4)}
            {copied ? (
              <FiCheck
                className="ml-2 text-white"
                width={16}
                height={16}
                // style={{ width: "16px", height: "16px" }}
              />
            ) : (
              <FiCopy
                className="ml-2"
                width={16}
                height={16}
                // style={{ width: "16px", height: "16px" }}
              />
            )}
          </button>
          <button
            className="pr-2 flex justify-center items-center"
            onClick={disconnect}
          >
            <FiLogOut size={20} />
          </button>
        </div>
      ) : (
        <button
          className="flex items-center md:px-4 px-4 py-2 border w-full border-white/20 rounded-lg text-center"
          onClick={connect}
        >
          <div className="flex flex-row gap-2 w-full items-center justify-center">
            <Image
              src="/assets/wallet_icon.svg"
              alt="wallet icon"
              width={20}
              height={20}
            />
            <p className="text-white whitespace-nowrap">Connect Wallet</p>
          </div>
        </button>
      )}
    </div>
  );
};
