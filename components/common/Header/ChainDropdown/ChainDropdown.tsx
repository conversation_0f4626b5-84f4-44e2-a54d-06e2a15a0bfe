import Image from "next/image";
import { useState } from "react";
import { use<PERSON>hain, useManager } from "@cosmos-kit/react";
import { useDetectBreakpoints } from "@/hooks";
import { useChainStore } from "@/store";
import { chainOptions } from "@/config";

export const ChainDropdown = () => {
  const { selectedChain, setSelectedChain } = useChainStore();
  const { chain } = useChain(selectedChain);
  const [input, setInput] = useState<string>(chain.pretty_name);
  const { isMobile } = useDetectBreakpoints();
  const { getChainLogo } = useManager();
  const [isOpen, setIsOpen] = useState(false);

  const onOpenChange = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative w-full min-w-[210px]">
      <div
        className="flex items-center border border-white/20 px-4 rounded-lg py-2 md:px-4 cursor-pointer justify-between bg-transparent"
        onClick={onOpenChange}
      >
        {input === chain.pretty_name && (
          <div className="flex flex-row">
            <Image
              src={getChainLogo(selectedChain) ?? ""}
              alt={chain.pretty_name}
              width={24}
              height={24}
              className="rounded-full mr-2"
            />
            <span>{input}</span>
          </div>
        )}
        <Image src="/assets/down_icon.svg" alt="" width={15} height={15} />
      </div>

      {isOpen && (
        <div className="absolute left-0 w-full bg-main-1400 border border-white/20 rounded-lg my-1 px-4 shadow-md z-50">
          {chainOptions.map((c, index) => (
            <div key={c.chain_name}>
              <div
                className="flex items-center cursor-pointer py-2"
                onClick={() => {
                  setInput(c.pretty_name);
                  setSelectedChain(c.chain_name);
                  setIsOpen(false);
                }}
              >
                <Image
                  src={getChainLogo(c.chain_name) ?? ""}
                  alt={c.pretty_name ?? "CHAIN"}
                  width={isMobile ? 18 : 24}
                  height={isMobile ? 18 : 24}
                  className="rounded-full mr-2"
                />
                <span>{c.pretty_name}</span>
              </div>
              {index < chainOptions.length - 1 && (
                <div className="h-[0.5px] bg-main-1700" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
