import { Header } from "./Header";
import { Footer } from "./Footer";
import { Sidebar } from "./Sidebar";
import { useDetectBreakpoints, useDisclosure } from "@/hooks";
import { BgGradient } from "../BgGradient";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { Breadcrumb } from "../Breadcrumbs";

export function Layout({ children }: { children: React.ReactNode }) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const pathname = usePathname();
  const rawSegments = pathname.split("/").filter(Boolean);
  const { isMobile } = useDetectBreakpoints();

  const pathSegments =
    rawSegments.includes("staking") && rawSegments.includes("validator-details")
      ? ["staking", "Validator Details"]
      : rawSegments.map((segment, index) =>
          rawSegments[index - 1] === "validator-details" ? "Details" : segment
        );

  return (
    <div className="flex flex-row overflow-hidden h-screen">
      <BgGradient />
      <Sidebar isOpen={isOpen} onClose={onClose} />
      <div className="flex flex-col overflow-auto without-scrollbar text-main-600 px-4 md:px-10 py-4 flex-1">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="whitespace-nowrap w-full pt-2 md:pt-0 flex flex-row justify-between">
            <Breadcrumb pathSegments={pathSegments} />
            {isMobile && (
              <button
                onClick={onOpen}
                className="md:hidden flex justify-end h-full items-center"
              >
                <Image
                  src="/assets/hamburger_icon.svg"
                  alt="hamburger icon"
                  width={24}
                  height={24}
                />
              </button>
            )}
          </div>
          <Header isOpen={onOpen} />
        </div>
        <div className="flex-1">{children}</div>
        <Footer />
      </div>
    </div>
  );
}
