import { ColumnConfig, TableRow } from "@empe/front-kit-next-ui";
import React from "react";
import { CollapsibleSection } from "../CollapsibleSection";

interface Props {
  data: TableRow[];
  columns: ColumnConfig[];
}

export const MobileTableContent = ({ data, columns }: Props) => {
  const prepareContent = (
    data:
      | JSX.Element
      | React.ReactNode
      | {
          value: string | number | React.ReactNode | JSX.Element;
          sortKey?: string | number;
        },
    column: ColumnConfig
  ) => {
    let cellData = data;
    let content: React.ReactNode | string | number | JSX.Element = null;

    if (cellData && typeof cellData === "object" && "value" in cellData) {
      data = cellData.value;
    } else if (column.format && cellData) {
      content = column.format(cellData);
    } else {
      content = cellData as any;
    }

    return content;
  };

  return (
    <div>
      {data.map((row, rowIndex) => {
        const header = row[columns[0].key];
        const contentHeader = prepareContent(header, columns[0]);
        return (
          <div key={rowIndex} className={"flex flex-col"}>
            <CollapsibleSection title={contentHeader}>
              {columns.slice(1).map((column) => {
                let cellData = row[column.key];
                const content = prepareContent(cellData, column);

                return (
                  <div key={column.key} className={"flex flex-col gap-1 py-2"}>
                    <span className="text-sm font-italic text-main-600/90">
                      {column.label}
                    </span>
                    <div>{content}</div>
                  </div>
                );
              })}
            </CollapsibleSection>
          </div>
        );
      })}
    </div>
  );
};
