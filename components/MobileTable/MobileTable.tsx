import { ColumnConfig, TableRow, useTableSort } from "@empe/front-kit-next-ui";
import React from "react";
import { MobileTableContent } from "./MobileTableContent";

interface Props {
  data: TableRow[];
  columns: ColumnConfig[];
}

export const MobileTable = ({ data, columns }: Props) => {
  const { sortedData, sortField, sortOrder, toggleSortOrder } = useTableSort(
    data,
    columns
  );

  // Preprocess data to extract values from objects with value property
  const processedData = React.useMemo(() => {
    return sortedData.map((row) => {
      const newRow: TableRow = {};

      // Process each cell in the row
      Object.keys(row).forEach((key) => {
        const cellData = row[key];

        // If the cell data is an object with a value property, extract the value
        if (
          cellData &&
          typeof cellData === "object" &&
          !React.isValidElement(cellData) &&
          "value" in cellData
        ) {
          newRow[key] = cellData.value;
        } else {
          newRow[key] = cellData;
        }
      });

      return newRow;
    });
  }, [sortedData]);

  return (
    <div className="p-2 bg-main-1700 text-white rounded-lg shadow-lg">
      {data.length === 0 ? (
        <p className="text-center text-gray-400">No data available</p>
      ) : (
        <div className="overflow-x-auto without-scrollbar">
          <MobileTableContent data={processedData} columns={columns} />
        </div>
      )}
    </div>
  );
};
