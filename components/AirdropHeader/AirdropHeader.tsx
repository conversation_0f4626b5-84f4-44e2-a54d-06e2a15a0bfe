import Link from "next/link";
import { socialLinks } from "../Soon/socialLinks";
import Image from "next/image";

export const AirdropHeader = () => {
  return (
    <div className="bg-main-400 flex flex-col gap-6 px-16 py-6 justify-center items-center rounded-lg max-w-4xl mx-auto">
      <Image
        src="/assets/empe_image.png"
        alt="empe image"
        width={75}
        height={75}
      />
      <div className="flex flex-col gap-4 justify-center items-center">
        <div className="flex md:flex-col justify-center items-center gap-2 text-center flex-col">
          <p className="md:text-2xl bg-gradient-to-r from-[#08a6fe] via-[#804bf4] to-[#d509ed] text-transparent bg-clip-text font-bold">
            Empeiria’s Airdrop Confirmed
          </p>
          <p className="md:text-xl">
            Perform on-chain activities, engage on X & join events to generate
            airdrop rewards!
          </p>
        </div>
      </div>
      {/* <div className="flex md:flex-row gap-1 bg-main-900 text-main-1100 py-2 px-4 rounded-lg text-center">
        Check your rewards on Airdrop Checker: Soon
      </div> */}
      <div className="flex flex-wrap md:flex-row md:gap-10 gap-4 justify-center items-center">
        {socialLinks.map(({ href, src, alt, size }, index) => (
          <Link key={index} href={href}>
            <Image
              src={src}
              alt={alt}
              width={35}
              height={35}
              className="w-[32px] h-[32px] md:w-[48px] md:h-[48px]"
            />
          </Link>
        ))}
      </div>
    </div>
  );
};
