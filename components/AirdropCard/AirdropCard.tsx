import { AirdropEvent } from "@/types/airdropEvents";
import { addLinkInString } from "@/utils/addLinkinStringDesc";
import { ButtonGradient } from "@empe/front-kit-next-ui";
import Image from "next/image";

export const AirdropCard = ({
  image,
  title,
  description,
  links,
  buttonText,
  type,
  status,
  onClick,
}: AirdropEvent) => {
  const renderDescription = () => {
    if (!description) return;
    return (
      <p
        className="text-sm"
        dangerouslySetInnerHTML={{ __html: addLinkInString(description) }}
      />
    );
  };

  const rednerLinks = () => {
    if (!links) return;
    return (
      <ol className="flex flex-col gap-2">
        {links.map((link, index) => (
          <li key={index}>
            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-main-100 hover:text-main-200 underline"
            >
              {link.label}
            </a>
          </li>
        ))}
      </ol>
    );
  };

  return (
    <div className="bg-main-1700/80 overflow-hidden h-[470px] flex flex-col w-[350px] rounded-2xl">
      <Image
        src={image}
        alt="airdrop"
        width={350}
        height={350}
        className="rounded-b-xl"
      />
      <div className="p-4 flex flex-col justify-between h-full">
        <div className="text-center flex flex-col gap-4">
          <h1 className="text-xl font-semibold">{title}</h1>
          {renderDescription()}
          {rednerLinks()}
        </div>
        <div className="flex flex-col gap-4">
          <div className="flex justify-between">
            <div className="flex flex-col gap-1">
              <span className="text-xs">Type</span>
              <div className="border rounded-lg px-1 border-main-100">
                {type}
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <span className="text-xs">Status</span>
              <div className="border rounded-lg px-1 border-[#21C900]">
                {status}
              </div>
            </div>
          </div>
          <ButtonGradient onClick={onClick}>{buttonText}</ButtonGradient>
        </div>
      </div>
    </div>
  );
};
