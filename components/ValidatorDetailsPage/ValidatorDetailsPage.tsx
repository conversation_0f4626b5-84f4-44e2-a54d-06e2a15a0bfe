"use client";

import {
  ValidatorDetails<PERSON>son<PERSON>iew,
  ValidatorHeader,
} from "@/components/ValidatorDetails";
import { WalletNotConnect } from "@/components/WalletNotConnect";
import { useStakingModals } from "@/contexts/StakingModalsContext";
import { useValidatorDetails } from "@/hooks/validator/useValidatorDetails";
import { useChain } from "@cosmos-kit/react";
import { useRouter } from "next/router";
import { Spinner } from "../Loaders";
import { useEffect, useState } from "react";
import { Box } from "../Box";

interface Props {
  chainName: string;
}

export const ValidatorDetailsPage = ({ chainName }: Props) => {
  const { isWalletConnected } = useChain(chainName);
  const { renderModals } = useStakingModals();
  const router = useRouter();
  const { id } = router.query;
  const [error, setError] = useState<string | null>(null);

  const validatorAddress = typeof id === "string" ? id : "";

  const {
    validatorDetails,
    validatorInfo,
    isLoading: isDataLoading,
    error: dataError,
  } = useValidatorDetails({
    operatorAddress: validatorAddress,
  });

  useEffect(() => {
    if (dataError) {
      setError("Failed to load validator details. Please try again later.");
    } else {
      setError(null);
    }
  }, [id, isDataLoading, dataError]);

  if (!isWalletConnected) {
    return (
      <div className="flex justify-center items-center h-full">
        <WalletNotConnect />
      </div>
    );
  }

  if (isDataLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  if (!validatorDetails || !validatorInfo) {
    return (
      <div
        className="flex justify-center items-center h-64"
        data-testid="validator-no-data"
      >
        <p>No validator data available.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      <ValidatorHeader
        name={validatorInfo.name}
        nick={validatorInfo.nick}
        avatarUrl={validatorInfo.avatarUrl}
      />
      <Box>
        <ValidatorDetailsJsonView
          data={validatorDetails}
          title="Validator Details"
          description="Information about the validator"
          addressKey="operator_address"
        />
      </Box>
      {renderModals()}
    </div>
  );
};
