import { ReactNode, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import clsx from "clsx";
import Image from "next/image";

type CollapsibleSectionProps = {
  title: string | ReactNode;
  children: ReactNode;
  initiallyExpanded?: boolean;
  containerClassName?: string;
  headerClassName?: string;
  contentClassName?: string;
  icon?: ReactNode;
};

export const CollapsibleSection = ({
  title,
  children,
  initiallyExpanded = false,
  containerClassName = "",
  headerClassName = "",
  contentClassName = "",
  icon,
}: CollapsibleSectionProps) => {
  const [expanded, setExpanded] = useState(initiallyExpanded);

  const toggleExpand = () => setExpanded((prev) => !prev);

  return (
    <div className={clsx("my-2 overflow-hidden", containerClassName)}>
      <button
        className={clsx(
          "w-full flex justify-between items-center px-4 py-2 text-left focus:outline-none",
          headerClassName
        )}
        aria-expanded={expanded}
        aria-controls="collapsible-content"
      >
        <div className="text-base font-semibold text-gray-800 dark:text-gray-100">
          {title}
        </div>

        <motion.div
          onClick={toggleExpand}
          animate={{ rotate: expanded ? 180 : 0 }}
          transition={{ duration: 0.2 }}
          className="ml-2"
        >
          {icon || (
            <Image
              src="/assets/down_icon.svg"
              alt="Expand"
              width={24}
              height={24}
            />
          )}
        </motion.div>
      </button>

      <AnimatePresence initial={false}>
        {expanded && (
          <motion.div
            id="collapsible-content"
            initial="collapsed"
            animate="open"
            exit="collapsed"
            variants={{
              open: { height: "auto", opacity: 1 },
              collapsed: { height: 0, opacity: 0 },
            }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
            className={clsx("overflow-hidden px-4", contentClassName)}
          >
            <div className="py-2">{children}</div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
