import { Wallet } from "@interchain-ui/react";
import Image from "next/image";
interface Props {
  wallets: Wallet[];
  onWalletItemClick?: (wallet: any) => void;
}
export const ConnectModalWalletList = ({
  wallets,
  onWalletItemClick,
}: Props) => {
  const renderFirstRow = wallets.slice(0, 2).map((wallet) => (
    <button
      key={`wallet-${wallet.name}`}
      className="flex items-center justify-center w-28 h-28 flex-col gap-2 border border-main-1100 hover:border-transparent border-gradient-hover rounded-xl"
      onClick={() => onWalletItemClick(wallet.originalWallet)}
    >
      <Image src={wallet.logo} alt={wallet.name} width={48} height={48} />
      <span className="text-sm">{wallet.prettyName}</span>
    </button>
  ));

  const renderLatestRow = wallets.slice(2).map((wallet) => (
    <button
      key={`wallet-${wallet.name}`}
      className="flex items-center justify-start p-4 w-full h-16 flex-row gap-2 border border-main-1100 hover:border-transparent border-gradient-hover rounded-xl"
      onClick={() => onWalletItemClick(wallet.originalWallet)}
    >
      <Image src={wallet.logo} alt={wallet.name} width={32} height={32} />
      <span className="text-sm">{wallet.prettyName}</span>
    </button>
  ));

  return (
    <div className="flex flex-wrap w-full px-4 text-main-600 justify-between items-center gap-4">
      {renderFirstRow}
      <div className="flex flex-col gap-4 w-full">{renderLatestRow}</div>
    </div>
  );
};
