import Image from "next/image";
import { ButtonSmallHover } from "../Buttons";

interface Props {
  title: string;
  hasBackButton?: boolean;
  hasCloseButton?: boolean;
  onClose: (event: any) => void;
  onBack?: (event: any) => void;
  id?: string;
  titleProps?: any;
  closeButtonProps?: any;
}

export const ConnectModalHead = ({
  title,
  hasBackButton = false,
  hasCloseButton = true,
  onClose,
  onBack,
}: Props) => {
  const renderBackButton = () => (
    <ButtonSmallHover onClick={onBack}>
      <Image src="/assets/back.svg" alt="Back" width={10} height={18} />
    </ButtonSmallHover>
  );

  const renderCloseButton = () => (
    <ButtonSmallHover onClick={onClose}>
      <Image src="/assets/close.svg" alt="Close" width={18} height={18} />
    </ButtonSmallHover>
  );

  return (
    <div className="w-full px-4 text-main-600 h-16 flex items-center justify-between">
      <div className="flex flex-1 items-center justify-start">
        {hasBackButton && renderBackButton()}
      </div>
      <div className="text-main-600 text-md font-bold">{title}</div>
      <div className="flex flex-1 items-center justify-end">
        {hasCloseButton && renderCloseButton()}
      </div>
    </div>
  );
};
