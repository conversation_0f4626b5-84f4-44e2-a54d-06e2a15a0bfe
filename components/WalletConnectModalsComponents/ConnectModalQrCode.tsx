import { QRCodeStatus } from "@interchain-ui/react";
import QRCode from "qrcode";
import { ButtonBorder } from "../Buttons";
import { Spinner } from "../Loaders";
import { useEffect, useRef } from "react";

interface Props {
  status: QRCodeStatus;
  link: string;
  description: string;
  qrCodeSize?: number;
  errorTitle?: any;
  errorDesc?: any;
  onRefresh?: () => void;
}

export const ConnectModalQrCode = ({
  status,
  link,
  description,
  qrCodeSize,
  errorTitle,
  errorDesc,
  onRefresh,
}: Props) => {
  const svgRef = useRef(null);

  useEffect(() => {
    if (link) {
      QRCode.toString(link, { type: "svg" }, (error, svgString) => {
        if (error) {
          console.error(error);
          return;
        }
        svgRef.current.innerHTML = svgString;
      });
    }
  }, [link]);

  const renderError = () => {
    return (
      <div className="flex flex-col items-center justify-center">
        <h2>{errorTitle}</h2>
        <p>{errorDesc}</p>
        {onRefresh && (
          <ButtonBorder onClick={onRefresh}>
            <div className="text-main-600 px-8">Refresh</div>
          </ButtonBorder>
        )}
      </div>
    );
  };

  const renderLoading = () => {
    return <Spinner />;
  };

  const renderQrCode = () => {
    if (!link) return null;
    return (
      <div className="text-white p-4 rounded-lg border w-full h-full border-main-600">
        <div ref={svgRef} className="w-full h-full" />
      </div>
    );
  };

  const renderContent = () => {
    switch (status) {
      case "Pending":
        return renderLoading();
      case "Error":
      case "Expired":
        return renderError();
      case "Done":
        return renderQrCode();
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col text-main-600 gap-6 justify-center items-center text-center w-full px-6">
      <div>{description}</div>
      {renderContent()}
    </div>
  );
};
