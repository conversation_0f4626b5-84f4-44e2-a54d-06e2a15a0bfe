import { ConnectedInfo, Wallet } from "@interchain-ui/react";
import Image from "next/image";
import { ButtonBorder } from "../Buttons";
import Link from "next/link";

interface Props {
  wallet: Wallet;
  status:
    | "Disconnected"
    | "Connecting"
    | "Connected"
    | "NotExist"
    | "Rejected"
    | "Error";
  contentHeader?: string;
  contentDesc?: string;
  bottomLink?: string;
  onConnect?: () => void;
  connectedInfo?: ConnectedInfo;
  onDisconnect?: () => void;
  onChangeWallet?: () => void;
  disableInstall?: boolean;
  onInstall?: () => void;
  installIcon?: React.ReactNode;
}

export const ConnectModalStatus = ({
  wallet,
  status,
  contentHeader,
  contentDesc,
  bottomLink,
  onConnect,
  onInstall,
  connectedInfo,
  onDisconnect,
  installIcon,
  onChangeWallet,
}: Props) => {
  const renderWalletIcon = () => {
    return (
      <div
        key={`wallet-${wallet.name}`}
        className="flex items-center justify-center w-28 h-28 flex-col gap-2 border border-ext-100 rounded-full"
      >
        <Image
          src={wallet.logo}
          alt={wallet.name}
          width={48}
          height={48}
          className="w-16 h-16 rounded-full"
        />
      </div>
    );
  };

  const renderContentForStatusDisconnected = () => {
    return (
      <div className="flex flex-col gap-1 items-center">
        {renderWalletIcon()}
        <p className="text-sm">Wallet is disconnected</p>
        <ButtonBorder onClick={onConnect}>
          <span className="px-4">Connect wallet</span>
        </ButtonBorder>
        {bottomLink && (
          <Link
            href={bottomLink}
            target="_blank"
            rel="noreferrer"
            className="mt-2 bg-main-100"
          >
            {"Don't have a wallet?"}
          </Link>
        )}
      </div>
    );
  };

  const renderContentForStatusConnecting = () => {
    return (
      <div className="flex flex-col gap-1 items-center">
        {renderWalletIcon()}
        <p className="text-sm text-center">{contentHeader}</p>
        <p className="text-sm text-center">{contentDesc}</p>
      </div>
    );
  };

  const renderContentForStatusConnected = () => {
    return (
      <div className="flex flex-col gap-1 items-center">
        {renderWalletIcon()}
        <p className="text-sm">{connectedInfo?.name}</p>
        <ButtonBorder onClick={onDisconnect}>Disconnect</ButtonBorder>
      </div>
    );
  };

  const renderContentForStatusNotExist = () => {
    return (
      <div className="flex flex-col justify-center items-center gap-4 text-main-600">
        {renderWalletIcon()}
        {/* CONTENT */}
        <div className="flex flex-col gap-2 items-center justify-center text-center">
          <span className="text-lg text-ext-100 text-center">
            {contentHeader}
          </span>
          <span className="text-sm text-center">{contentDesc}</span>
        </div>

        {/* BOTTOM LINK */}
        <ButtonBorder onClick={onInstall}>
          <span className="text-sm flex flex-row items-center gap-2 px-4">
            {installIcon}
            Install {wallet.prettyName || wallet.name}
          </span>
        </ButtonBorder>
      </div>
    );
  };

  const renderContentForStatusRejected = () => {
    return (
      <div className="flex flex-col gap-1 items-center">
        {renderWalletIcon()}
        <p className="text-sm text-center">{contentHeader}</p>
        <p className="text-sm text-center">{contentDesc}</p>
        <ButtonBorder onClick={onConnect}>
          <span className="px-4">Reconnect</span>
        </ButtonBorder>
      </div>
    );
  };

  const renderContentForStatusError = () => {
    return (
      <div className="flex flex-col gap-1 items-center">
        {renderWalletIcon()}
        <p className="text-sm text-center">{contentHeader}</p>
        <p className="text-sm text-center">{contentDesc}</p>
        <ButtonBorder onClick={onChangeWallet}>
          <span className="px-4">Change wallet</span>
        </ButtonBorder>
      </div>
    );
  };

  const renderContent = () => {
    switch (status) {
      case "Disconnected":
        return renderContentForStatusDisconnected();
      case "Connecting":
        return renderContentForStatusConnecting();
      case "Connected":
        return renderContentForStatusConnected();
      case "NotExist":
        return renderContentForStatusNotExist();
      case "Rejected":
        return renderContentForStatusRejected();
      case "Error":
        return renderContentForStatusError();
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col justify-center items-center gap-4 text-main-600">
      {renderContent()}
    </div>
  );
};
