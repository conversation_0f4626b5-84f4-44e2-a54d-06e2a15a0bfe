import Link from "next/link";
import { LineSeparator } from "../Separators";
interface Props {
  title: string;
  showAllCallback?: (() => void) | string;
}

export const DashboardBoxHeader = ({ title, showAllCallback }: Props) => {
  const renderShowAllCallback = () => {
    if (typeof showAllCallback === "function") {
      return (
        <button
          onClick={showAllCallback}
          className="text-xs cursor-pointer underline hover:text-main-100"
        >
          Show all
        </button>
      );
    }

    if (typeof showAllCallback === "string") {
      return (
        <Link
          href={showAllCallback}
          target="_blank"
          className="text-xs cursor-pointer underline hover:text-main-100"
        >
          Show all
        </Link>
      );
    }

    return null;
  };

  return (
    <>
      <div className="flex flex-row justify-between items-center mb-4">
        <span className="text-lg">{title}:</span>
        {renderShowAllCallback()}
      </div>
      <LineSeparator />
    </>
  );
};
