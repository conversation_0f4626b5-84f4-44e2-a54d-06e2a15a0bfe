import Image from "next/image";
import Link from "next/link";
import { socialLinks } from "./socialLinks";

export const AirDropSoon = () => {
  return (
    <div className="flex justify-center items-center min-h-full">
      <div className="bg-main-400 flex flex-col md:gap-10 gap-4 py-8 px-8 md:py-16 md:px-32 justify-center items-center rounded-lg max-w-4xl mx-auto">
        <Image
          src="/assets/empe_image.png"
          alt="empe image"
          width={75}
          height={75}
        />
        <div className="flex flex-col gap-2 justify-center items-center">
          <div className="flex md:flex-row gap-1 whitespace-nowrap flex-col">
            <p className="md:text-xl bg-gradient-to-r from-[#08a6fe] via-[#804bf4] to-[#d509ed] text-transparent bg-clip-text font-bold">
              Empeiria’s Airdrop
            </p>
            <p className="md:text-xl">will be available soon!</p>
          </div>
          <div className="flex md:flex-row gap-1 text-center">
            <p className="text-md">
              Stay tuned to the{" "}
              <Link href="/" className="underline">
                announcements section
              </Link>{" "}
              or our social media!
            </p>
          </div>
        </div>
        <div className="flex flex-row md:gap-10 gap-4 justify-center items-center">
          {socialLinks.map(({ href, src, alt, size }, index) => (
            <Link key={index} href={href}>
              <Image
                src={src}
                alt={alt}
                width={35}
                height={35}
                className="w-[32px] h-[32px] md:w-[48px] md:h-[48px]"
              />
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};
