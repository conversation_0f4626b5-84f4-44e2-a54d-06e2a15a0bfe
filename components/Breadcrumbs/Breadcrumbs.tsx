import React from "react";
import { <PERSON>Back } from "./ArrowBack";
import Link from "next/link";
interface Props {
  pathSegments: string[];
  separatorIcon?: React.ReactNode | JSX.Element;
}

const defaultSeparatorIcon = <ArrowBack className="w-4 h-4 fill-main-600" />;

export const Breadcrumb = ({
  pathSegments,
  separatorIcon = defaultSeparatorIcon,
}: Props) => {
  const segments = pathSegments.length === 0 ? ["start"] : pathSegments;

  const formatSegmentName = (segment: string) => {
    const customNames: Record<string, string> = {
      start: "Home",
    };

    return (
      customNames[segment] ||
      segment.replace(/-/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())
    );
  };

  return (
    <div className="flex items-center text-main-600 gap-2">
      {segments.map((segment, index) => {
        const href = `/${segments.slice(0, index + 1).join("/")}`;
        const isLast = index === segments.length - 1;

        return (
          <div key={href} className="flex items-center gap-2">
            {!isLast ? (
              <Link href={href} className="hover:underline">
                {formatSegmentName(segment)}
              </Link>
            ) : (
              <span className="text-main-100 underline flex items-center gap-1">
                {formatSegmentName(segment)}
              </span>
            )}

            {!isLast && separatorIcon}
          </div>
        );
      })}
    </div>
  );
};
