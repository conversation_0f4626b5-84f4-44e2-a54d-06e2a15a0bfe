import { useState, useMemo } from "react";
import Image from "next/image";
import Link from "next/link";
import { cutLongString } from "@/utils/cutLongString";

interface Props {
  data: Record<string, any>;
  title?: string;
  description?: string;
  addressKey?: string; // Key containing the address to copy
  excludeFields?: string[]; // Fields to exclude from rendering
}

export const ValidatorDetailsJsonView = ({
  data,
  title = "Validator Details",
  description = "",
  addressKey = "operator_address",
  excludeFields = ["avatar_url"], // Default exclude avatar_url
}: Props) => {
  const [copied, setCopied] = useState(false);

  // Function to flatten nested JSON object
  const flattenObject = (obj: any, prefix = ""): Record<string, any> => {
    return Object.keys(obj).reduce((acc: Record<string, any>, key: string) => {
      const pre = prefix.length ? `${prefix}.` : "";

      // Check if field should be excluded
      if (excludeFields.includes(key)) {
        return acc;
      }

      if (
        typeof obj[key] === "object" &&
        obj[key] !== null &&
        !Array.isArray(obj[key])
      ) {
        Object.assign(acc, flattenObject(obj[key], pre + key));
      } else if (
        Array.isArray(obj[key]) &&
        obj[key].length > 0 &&
        typeof obj[key][0] === "object"
      ) {
        // For arrays of objects, take the first element
        Object.assign(acc, flattenObject(obj[key][0], pre + key));
      } else {
        acc[pre + key] = obj[key];
      }

      return acc;
    }, {});
  };

  // Memoize flattened data to improve performance
  const flattenedData = useMemo(
    () => flattenObject(data),
    [data, excludeFields]
  );

  // Find address to copy
  const addressToCopy = Object.keys(flattenedData).find(
    (key) => key.includes(addressKey) || key.endsWith("address")
  );

  const address = addressToCopy ? flattenedData[addressToCopy] : "";

  // Prepare data for display
  const displayItems = useMemo(() => {
    return Object.entries(flattenedData)
      .filter(([key]) => {
        // Exclude address and fields from excludeFields list
        if (key === addressToCopy) return false;

        // Check if key contains any of the excluded fields
        return !excludeFields.some((field) => key.includes(field));
      })
      .map(([key, value]) => {
        // Format key for display
        const label = key.split(".").pop() || key;
        const formattedLabel = label
          .replace(/_/g, " ")
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase());

        // Format value
        let formattedValue = value;
        if (key.toLowerCase().includes("commission")) {
          const numValue = parseFloat(value);
          formattedValue = isNaN(numValue)
            ? value
            : `${(numValue * 100).toFixed(1)}%`;
        } else if (typeof value === "boolean") {
          formattedValue = value ? "Yes" : "No";
        } else if (value === null || value === undefined) {
          formattedValue = "N/A";
        } else if (
          typeof value === "number" &&
          key.toLowerCase().includes("votingpower")
        ) {
          formattedValue = `${value.toLocaleString("en-US")}%`;
        }

        // Check if value is a link
        const isLink =
          typeof value === "string" &&
          (value.startsWith("http://") || value.startsWith("https://"));

        return { label: formattedLabel, value: formattedValue, isLink };
      });
  }, [flattenedData, addressToCopy, excludeFields]);

  const copyToClipboard = () => {
    if (address) {
      navigator.clipboard.writeText(address).then(() => {
        setCopied(true);
        const timeoutId = setTimeout(() => setCopied(false), 2000);
        return () => clearTimeout(timeoutId);
      });
    }
  };

  // Function to render value (as link or plain text)
  const renderValue = (value: any, isLink: boolean) => {
    if (isLink) {
      return (
        <Link
          href={value}
          target="_blank"
          rel="noopener noreferrer"
          className="text-right max-w-[60%] break-words text-blue-400 hover:text-blue-300 underline"
        >
          {value}
        </Link>
      );
    }

    return <p className="text-right max-w-[60%] break-words">{value}</p>;
  };

  return (
    <>
      <div className="px-2 py-8 space-y-4">
        {address && (
          <div className="flex justify-between border-t-[0.5px] pt-4 border-white/10">
            <p>Address:</p>
            <div className="flex items-center gap-4">
              <p className="text-right block md:hidden">
                {cutLongString(address)}
              </p>
              <p className="text-right hidden md:block">{address}</p>
              <Image
                src={
                  copied
                    ? "/assets/check_icon_white.svg"
                    : "/assets/clipboard_icon.svg"
                }
                alt="clipboard icon"
                width={20}
                height={20}
                className="cursor-pointer"
                onClick={copyToClipboard}
              />
            </div>
          </div>
        )}

        {displayItems.map(({ label, value, isLink }) => (
          <div
            key={label}
            className="flex justify-between border-t-[0.5px] pt-4 border-white/10"
          >
            <p>{label}:</p>
            {renderValue(value, isLink)}
          </div>
        ))}
      </div>
    </>
  );
};
