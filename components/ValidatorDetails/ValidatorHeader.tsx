import Image from "next/image";
import { useStakingModals } from "@/contexts/StakingModalsContext";
import Link from "next/link";

interface Props {
  avatarUrl: string;
  name: string;
  nick: string;
}

export const ValidatorHeader = ({ name, nick, avatarUrl }: Props) => {
  // Default fallback image
  const fallbackImage = "/assets/validator_icon.svg";

  // Use fallback immediately if avatarUrl is empty
  const imageSource = avatarUrl || fallbackImage;

  const { openDelegateModal, openUndelegateModal, selectedValidator } =
    useStakingModals();

  // Shared button styles for consistent appearance
  const secondaryButtonStyle =
    "py-3 px-6 text-white font-semibold rounded-lg w-full border text-center";
  const primaryButtonStyle =
    "py-3 px-8 text-white font-semibold rounded-lg shadow-md w-full transition-all bg-gradient-to-r from-[#00AFFF] via-[#006EFF] to-[#E200ED] hover:from-[#0099E0] hover:via-[#005BCC] hover:to-[#C700D6]";

  // Handler functions with safety checks
  const handleUndelegate = () => {
    if (selectedValidator) {
      openUndelegateModal(selectedValidator);
    }
  };

  const handleDelegate = () => {
    if (selectedValidator) {
      openDelegateModal(selectedValidator);
    }
  };

  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 md:gap-4 w-full">
      <div className="flex gap-4 items-center w-full md:w-auto">
        <Image
          src={imageSource}
          onError={(e) => {
            e.currentTarget.src = fallbackImage;
          }}
          alt={`${name} avatar`}
          width={64}
          height={64}
          className="rounded-full"
        />
        <div className="flex flex-col items-start">
          <p className="text-lg md:text-xl font-semibold">
            {name || "Unknown Validator"}
          </p>
          <p className="text-sm md:text-xs text-main-600">
            {nick || "No description available"}
          </p>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-2 md:gap-4 w-full md:w-auto">
        <button
          onClick={handleUndelegate}
          className={secondaryButtonStyle}
          disabled={!selectedValidator}
        >
          Undelegate
        </button>
        <Link
          href={`/staking/select-validator`}
          className={secondaryButtonStyle}
        >
          Redelegate
        </Link>
        <button
          onClick={handleDelegate}
          className={primaryButtonStyle}
          disabled={!selectedValidator}
        >
          Delegate
        </button>
      </div>
    </div>
  );
};
