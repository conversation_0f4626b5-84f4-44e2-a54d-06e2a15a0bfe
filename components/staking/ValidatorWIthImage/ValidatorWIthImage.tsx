"use client";

import { memo, useMemo, useState, useRef } from "react";
import Image from "next/image";
import { type ExtendedValidator as Validator } from "@/utils";

export const ValidatorWithImage = memo(
  ({
    validator,
    staticRow = false,
    logos,
  }: {
    validator: Validator;
    staticRow?: boolean;
    logos?: { [key: string]: string };
  }) => {
    const defaultImg = "/assets/validator_icon.svg";
    const [isLoading, setIsLoading] = useState(true);
    const [imgError, setImgError] = useState(false);
    const errorCountRef = useRef(0);
    const maxErrorAttempts = 2; // Maksymalna liczba prób ładowania

    const imgSrc = useMemo(() => {
      // Jeśli już wystąpił błąd, nie próbuj ponownie ładować obrazu
      if (imgError) return defaultImg;

      return logos && validator.address in logos
        ? logos[validator.address]
        : defaultImg;
    }, [logos, validator.address, imgError]);

    const handleImageError = (
      e: React.SyntheticEvent<HTMLImageElement, Event>
    ) => {
      errorCountRef.current += 1;

      // Jeśli przekroczono maksymalną liczbę prób, ustaw flagę błędu
      if (errorCountRef.current >= maxErrorAttempts) {
        setImgError(true);
      }

      e.currentTarget.src = defaultImg;
      setIsLoading(false);
    };

    return (
      <div
        className={`flex items-center gap-3  ${
          staticRow ? "flex-row" : "md:flex-row flex-col"
        }`}
      >
        <div className="relative w-[30px] h-[30px]">
          {isLoading && !imgError && (
            <div className="absolute inset-0 bg-main-400 rounded-full flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-main-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
          <Image
            src={imgError ? defaultImg : imgSrc}
            alt="validator image"
            width={30}
            height={30}
            onError={handleImageError}
            onLoad={() => setIsLoading(false)}
            className={`rounded-full ${
              isLoading && !imgError ? "opacity-0" : "opacity-100"
            }`}
          />
        </div>
        <p className="text-lg font-normal text-center md:text-left">
          {validator.name.slice(0, 16) + "..."}
        </p>
      </div>
    );
  }
);

ValidatorWithImage.displayName = "ValidatorWithImage";
