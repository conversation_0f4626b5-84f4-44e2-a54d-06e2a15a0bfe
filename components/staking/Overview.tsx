"use client";

import Image from "next/image";
import { ChainName } from "cosmos-kit";
import { getCoin } from "@/utils";
import { sum, calcDollarValue, type ParsedRewards as Rewards } from "@/utils";

interface StakingData {
  balance: string;
  rewards: Rewards;
  staked: string;
  prices: any;
}

interface Props {
  stakingData: StakingData;
  chainName: ChainName;
  updateData: () => void;
}

export const Overview = ({ stakingData, chainName }: Props) => {
  const { balance, rewards, staked, prices } = stakingData;
  const totalAmount = sum(balance, staked, rewards?.total ?? "0");
  const coin = getCoin(chainName);

  return (
    <div className="bg-main-400 rounded-xl w-full py-8 flex md:flex-row flex-col justify-center md:gap-24 gap-4 items-center text-white shadow-md px-4">
      <div className="flex flex-col md:flex-row items-center gap-4 md:gap-8">
        <Image
          src={"/assets/empe_image.png"}
          alt={coin.symbol}
          width={60}
          height={60}
        />
        <div className="flex flex-row items-center md:justify-start md:flex-col gap-4 md:gap-0">
          <p className="text-sm">Total:</p>
          <p className="text-xl flex flex-row gap-1">
            {totalAmount.toLocaleString()}
            <span className="text-main-800">{coin.symbol}</span>
          </p>
          <p className="text-main-800 text-sm">
            ≈ ${calcDollarValue(coin.base, totalAmount, prices)}
          </p>
        </div>
      </div>
      <div className="flex flex-row md:flex-col items-center md:justify-start gap-4 md:gap-0">
        <p className="text-sm">Available:</p>
        <p className="text-xl flex flex-row gap-1">
          {Number(balance).toLocaleString()}
          <span className="text-main-800">{coin.symbol}</span>
        </p>
        <p className="text-main-800 text-sm">
          ≈ ${calcDollarValue(coin.base, balance, prices)}
        </p>
      </div>
    </div>
  );
};
