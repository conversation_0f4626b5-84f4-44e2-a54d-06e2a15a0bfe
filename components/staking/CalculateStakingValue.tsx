import { getAssetLogoUrl } from "@/utils";
import { Asset } from "@chain-registry/types";
import Image from "next/image";
import { ChangeEvent } from "react";

interface Props {
  coin: Asset;
  error?: string;
  inputValue: string;
  isSimulating?: boolean;
  inputOnChange: (e: ChangeEvent<HTMLInputElement>) => void;
  buttonFirstOnClick: () => void;
  buttonSecondOnClick: () => void;
  buttonMaxOnClick: () => void;
}
export const CalculateStakingValue = ({
  coin,
  error,
  inputValue,
  isSimulating = false,
  inputOnChange,
  buttonFirstOnClick,
  buttonSecondOnClick,
  buttonMaxOnClick,
}: Props) => {
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="flex flex-row items-center gap-4 sm:gap-2 border border-white py-4 px-4 sm:px-6 rounded-lg sm:justify-between">
        <Image
          src={getAssetLogoUrl(coin)}
          alt="Coin Logo"
          width={40}
          height={40}
        />
        <input
          type="text"
          className="w-full sm:w-auto text-center bg-transparent text-white border-none outline-none text-xl"
          value={inputValue}
          onChange={inputOnChange}
        />
        <p className="text-white text-lg">{coin.symbol}</p>
      </div>
      {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
      <div className="flex flex-row justify-end py-4">
        <div className="bg-main-1600 flex gap-4 px-4 rounded-lg text-white text-sm">
          <button onClick={buttonFirstOnClick}>1/2</button>
          <button onClick={buttonSecondOnClick}>1/3</button>
          <button onClick={buttonMaxOnClick} disabled={isSimulating}>
            {isSimulating ? "Calculating..." : "Max"}
          </button>
        </div>
      </div>
    </div>
  );
};
