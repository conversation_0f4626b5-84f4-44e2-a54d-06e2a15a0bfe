"use client";

import { useState } from "react";
import { cosmos } from "@empe/empejs";
import { use<PERSON>hain } from "@cosmos-kit/react";
import { ChainName } from "cosmos-kit";
import BigNumber from "bignumber.js";
import { getCoin, getExponent } from "@/utils";
import { UseDisclosureReturn, useTx } from "@/hooks";
import {
  getAssetLogoUrl,
  isGreaterThanZero,
  toBaseAmount,
  type ExtendedValidator as Validator,
} from "@/utils";
import { BasicModal } from "./BasicModal";
import Image from "next/image";
import { StakingModalValidatorHeader } from "../StakingModalValidatorHeader";
import { CalculateStakingValue } from "./CalculateStakingValue";

const { undelegate } = cosmos.staking.v1beta1.MessageComposer.fromPartial;

interface Props {
  updateData: () => void;
  unbondingDays: string;
  chainName: ChainName;
  selectedValidator: Validator;
  closeOuterModal?: () => void;
  openValidatorDetailsSubpage: () => void;
  modalControl: UseDisclosureReturn;
  logoUrl: string;
}

export const UndelegateModal = ({
  updateData,
  unbondingDays,
  chainName,
  logoUrl,
  selectedValidator,
  closeOuterModal,
  modalControl,
  openValidatorDetailsSubpage,
}: Props) => {
  const [amount, setAmount] = useState<number | undefined>(
    Number(selectedValidator.delegation)
  );
  const [isUndelegating, setIsUndelegating] = useState(false);
  const [inputValue, setInputValue] = useState(
    selectedValidator.delegation.toString().replace(",", ".")
  );

  const { address } = useChain(chainName);
  const { tx } = useTx(chainName);

  const coin = getCoin(chainName);
  const exp = getExponent(chainName);

  const closeUndelegateModal = () => {
    setAmount(0);
    setIsUndelegating(false);
    modalControl.onClose();
  };

  const onUndelegateClick = async () => {
    if (!address || !amount) return;

    if (amount > Number(selectedValidator.delegation)) {
      alert("Cannot undelegate more than your current delegation amount");
      return;
    }

    setIsUndelegating(true);

    const msg = undelegate({
      delegatorAddress: address,
      validatorAddress: selectedValidator.address,
      amount: {
        amount: toBaseAmount(amount, exp),
        denom: coin.base,
      },
    });

    await tx([msg], {
      onSuccess: () => {
        updateData();
        if (closeOuterModal) {
          closeOuterModal();
        }
        closeUndelegateModal();
      },
    });

    setIsUndelegating(false);
  };

  const handleOpenValidatorDetailsSubpage = () => {
    openValidatorDetailsSubpage();
    closeUndelegateModal();
  };

  const maxAmount = selectedValidator.delegation;

  const headerExtra = (
    <>
      <div
        className="border border-t border-main-1400 border-1"
        style={{ borderWidth: "0.5px" }}
      ></div>
      <div className="bg-main-1300/20 rounded-lg my-8 py-4 px-10 flex flex-col gap-2 justify-center items-center text-white">
        {unbondingDays && (
          <div className="flex flex-col gap-3 w-full max-w-[450px] mx-auto">
            <div className="flex flex-row gap-2">
              <Image
                src="/assets/info_icon.svg"
                alt="info icon"
                width={25}
                height={25}
              />
              <p className="text-xl">{`Once the unbonding period begins you will:`}</p>
            </div>
            <ul className="text-main-800 text-sm text-left px-8">
              <li>• not receive staking rewards</li>
              <li>• not be able to cancel the unbonding</li>
              <li>
                • need to wait {unbondingDays} days for the amount to be liquid
              </li>
            </ul>
          </div>
        )}
      </div>
      <div
        className="border border-b border-main-1400 border-1"
        style={{ borderWidth: "0.5px" }}
      />
    </>
  );

  return (
    <BasicModal
      title="Undelegate"
      isOpen={modalControl.isOpen}
      onClose={closeUndelegateModal}
    >
      <div className="max-w-4xl bg-main-400 text-main-600 flex flex-col gap-6 lg:p-8 p-2">
        <StakingModalValidatorHeader
          onClose={handleOpenValidatorDetailsSubpage}
          selectedValidator={selectedValidator}
          logoUrl={logoUrl}
        />
        <div>{headerExtra}</div>
        <div className="bg-main-1500 flex items-center justify-center flex-col py-2 w-full md:w-1/2 rounded-lg mx-auto gap-1">
          <p className="text-main-800 text-sm">{"Your Delegation"}</p>
          <div className="flex flex-row gap-1 text-lg">
            <p className="text-white">{selectedValidator.delegation}</p>
            <p className="text-main-800">{coin.symbol}</p>
          </div>
        </div>

        <CalculateStakingValue
          coin={coin}
          inputValue={inputValue}
          inputOnChange={(e) => {
            let value = e.target.value.replace(/[^0-9.]/g, "");
            if (value.split(".").length > 2) {
              value = value.replace(/\.+$/, "");
            }
            setInputValue(value);
            const numericValue = parseFloat(value);
            setAmount(isNaN(numericValue) ? 0 : numericValue);
          }}
          buttonFirstOnClick={() => {
            if (selectedValidator.delegation) {
              const newAmount = new BigNumber(selectedValidator.delegation)
                .dividedBy(2)
                .toNumber();
              setAmount(newAmount);
              setInputValue(newAmount.toString().replace(",", "."));
            }
          }}
          buttonSecondOnClick={() => {
            if (selectedValidator.delegation) {
              const newAmount = new BigNumber(selectedValidator.delegation)
                .dividedBy(3)
                .toNumber();
              setAmount(newAmount);
              setInputValue(newAmount.toString().replace(",", "."));
            }
          }}
          buttonMaxOnClick={() => {
            if (selectedValidator.delegation) {
              setAmount(Number(selectedValidator.delegation));
              setInputValue(
                selectedValidator.delegation.toString().replace(",", ".")
              );
            }
          }}
        />

        <button
          className={`py-3 px-4 text-white mx-auto w-1/2 font-semibold rounded-lg shadow-md transition-all ${
            isGreaterThanZero(amount)
              ? "bg-gradient-to-r from-[#00AFFF] via-[#006EFF] to-[#E200ED] hover:from-[#0099E0] hover:via-[#005BCC] hover:to-[#C700D6]"
              : "bg-[#373737] cursor-not-allowed"
          }`}
          onClick={onUndelegateClick}
          disabled={!isGreaterThanZero(amount) || isUndelegating}
        >
          Undelegate
        </button>
      </div>
    </BasicModal>
  );
};
