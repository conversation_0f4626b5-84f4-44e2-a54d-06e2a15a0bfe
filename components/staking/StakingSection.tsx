"use client";

import { use<PERSON>hain } from "@cosmos-kit/react";
import { ChainName } from "cosmos-kit";
import { AllValidators } from "./AllValidators";
import { useStakingData, useValidatorLogos } from "@/hooks";
import { Overview } from "./Overview";
import { StakedBox } from "./StakedBox";
import { useEffect, useState } from "react";
import { ClaimableRewards } from "./Awards";
import { MyValidators } from "./MyValidators/MyValidators";
import { Spinner } from "@interchain-ui/react";
import clsx from "clsx";
import { useUptime } from "@/hooks/staking/useUptime";
import { useStakingModals } from "@/contexts/StakingModalsContext";
import { WalletNotConnect } from "../WalletNotConnect";

export const StakingSection = ({ chainName }: { chainName: ChainName }) => {
  const { isWalletConnected } = useChain(chainName);
  const { renderModals, setStakingConfig } = useStakingModals();

  const { data, isLoading, refetch } = useStakingData(chainName);
  const [staked, setStaked] = useState(0);

  // This function is no longer needed as claiming is handled by the modal
  // Kept for backward compatibility
  const handleClaim = () => {};

  const { logos } = useValidatorLogos();
  const { uptime } = useUptime();

  useEffect(() => {
    if (data) {
      setStakingConfig({
        balance: data.balance,
        updateData: refetch,
        unbondingDays: data.unbondingDays,
        chainName,
        logos,
        prices: data.prices,
        allValidators: data.allValidators,
      });
    }
  }, [data, chainName, logos, refetch, setStakingConfig]);

  return (
    <div
      className={clsx("flex flex-col items-center h-full", {
        "justify-start": isWalletConnected,
        "justify-center": !isWalletConnected,
      })}
    >
      {!isWalletConnected ? (
        <WalletNotConnect />
      ) : isLoading || !data ? (
        <div className="h-28 flex justify-center items-center">
          <Spinner size="$7xl" />
        </div>
      ) : (
        <div className="space-y-6 w-full">
          <div className="flex flex-col gap-6 justify-center items-center">
            <Overview
              stakingData={{
                balance: data.balance,
                rewards: data.rewards,
                staked: data.totalDelegated,
                prices: data.prices,
              }}
              updateData={refetch}
              chainName={chainName}
            />
            <div className="flex flex-col md:flex-row gap-6 w-full justify-center items-center">
              <div className="md:flex-1 w-full">
                <StakedBox staked={staked} chainName={chainName} />
              </div>
              <div className="md:flex-1 w-full">
                <ClaimableRewards
                  amount={data.rewards?.total || "0"}
                  rewards={data.rewards}
                  onClaim={handleClaim}
                  chainName={chainName}
                  updateData={refetch}
                />
              </div>
            </div>
          </div>
          {data.myValidators.length > 0 && (
            <MyValidators
              uptime={uptime}
              amountStaked={data.ammountStaked}
              myValidators={data.myValidators}
              chainName={chainName}
              logos={logos}
            />
          )}

          <AllValidators
            uptime={uptime}
            validators={data.allValidators}
            chainName={chainName}
            logos={logos}
            amountStaked={data.ammountStaked}
          />
          {renderModals()}
        </div>
      )}
    </div>
  );
};
