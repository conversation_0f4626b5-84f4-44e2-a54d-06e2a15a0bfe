"use client";

import { ChainName } from "cosmos-kit";
import MyValidatorsList from "./MyValidatorsList";
import { type ExtendedValidator as Validator } from "@/utils";
import { StakingUptime } from "@/types/stakingUptime";
import { useStakingModals } from "@/contexts/StakingModalsContext";
import { AmmountStaked } from "@/types/ammountStaked";

interface Props {
  myValidators: Validator[];
  amountStaked: AmmountStaked[];
  uptime: StakingUptime;
  chainName: ChainName;
  logos: {
    [key: string]: string;
  };
}

export const MyValidators = ({
  myValidators,
  amountStaked,
  uptime,
  chainName,
  logos,
}: Props) => {
  const { openValidatorInfoModal } = useStakingModals();

  return (
    <div className="w-full flex flex-col gap-2">
      <p className="text-white text-xl">My Validators:</p>
      <MyValidatorsList
        logos={logos}
        uptime={uptime}
        amountStaked={amountStaked}
        myValidators={myValidators}
        chainName={chainName}
        openModal={(validator) => openValidatorInfoModal(validator)}
      />
    </div>
  );
};
