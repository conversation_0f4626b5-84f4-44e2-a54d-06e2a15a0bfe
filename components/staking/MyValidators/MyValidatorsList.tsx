"use client";

import { memo } from "react";
import { ChainName } from "cosmos-kit";
import { getCoin, type ExtendedValidator as Validator } from "@/utils";
import { ButtonBasicSmall, TableBasic } from "@empe/front-kit-next-ui";
import { ValueWithSymbol } from "../ValueWithSymbol";
import { ValidatorWithImage } from "../ValidatorWIthImage";
import { stakingColumnsMyValidators } from "../config";
import { useDashboardStore } from "@/store/dashboardStore";
import { calculateVotingPower } from "@/utils/calculateVotingPower";
import { StakingUptime } from "@/types/stakingUptime";
import Link from "next/link";
import { useStakingModals } from "@/contexts/StakingModalsContext";
import { AmmountStaked } from "@/types/ammountStaked";
import { MobileTable } from "@/components/MobileTable/MobileTable";

interface Props {
  myValidators: Validator[];
  amountStaked: AmmountStaked[];
  uptime: StakingUptime;
  openModal: (validator: Validator) => void;
  chainName: ChainName;
  logos: { [key: string]: string };
}

const MyValidatorsList = ({
  myValidators,
  amountStaked = [],
  uptime,
  openModal,
  chainName,
  logos,
}: Props) => {
  const coin = getCoin(chainName);
  const { bondedTokens } = useDashboardStore();
  const { openValidatorDetailsSubpage } = useStakingModals();

  const data = myValidators.map((validator) => ({
    validator: {
      value: (
        <Link
          href={`/staking/validator-details/${validator.address}`}
          onClick={() => openValidatorDetailsSubpage(validator)}
          className="cursor-pointer"
        >
          <ValidatorWithImage
            validator={validator}
            logos={logos}
            staticRow={true}
          />
        </Link>
      ),
      sortKey: validator.name,
    },
    amount: {
      value: (
        <ValueWithSymbol
          value={
            amountStaked.find(
              (item) => item.validatorAddress === validator.address
            )?.amount || "0"
          }
          symbol={coin.symbol}
        />
      ),
      sortKey: Number(
        (
          amountStaked?.find(
            (item) => item.validatorAddress === validator.address
          )?.amount || "0"
        ).replace(/,/g, "")
      ),
    },
    commission: validator.commission,
    uptime: uptime[validator.address]?.uptime || 0,
    reward: {
      value: <ValueWithSymbol value={validator.reward} symbol={coin.symbol} />,
      sortKey: Number(validator.reward),
    },
    votingPower: calculateVotingPower(validator.votingPower, bondedTokens),
    votingPowerSortKey: Number(validator.votingPower),
    actions: (
      <div className="w-[120px] flex items-center justify-center mx-auto">
        <ButtonBasicSmall onClick={() => openModal(validator)}>
          Manage
        </ButtonBasicSmall>
      </div>
    ),
  }));

  return (
    <>
      <span className="hidden md:block">
        <TableBasic data={data} columns={stakingColumnsMyValidators} />
      </span>
      <span className="md:hidden">
        <MobileTable data={data} columns={stakingColumnsMyValidators} />
      </span>
    </>
  );
};

MyValidatorsList.displayName = "MyValidatorsList";

export default memo(MyValidatorsList);
