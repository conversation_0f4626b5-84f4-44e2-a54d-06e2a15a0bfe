"use client";
import Image from "next/image";
import { useEffect } from "react";
import { createPortal } from "react-dom";

export interface BasicModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children?: React.ReactNode;
}

export const BasicModal = ({
  isOpen,
  onClose,
  title,
  children,
}: BasicModalProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return createPortal(
    <div
      className="fixed inset-0 z-50 flex items-start justify-center p-4 bg-black bg-opacity-50 overflow-y-auto"
      onClick={onClose}
    >
      <div
        className="relative bg-main-400 rounded-2xl shadow-lg lg:py-6 lg:px-8 py-2 px-4 w-full max-w-4xl my-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-row justify-between items-center">
          {title && <h2 className="text-lg text-white p-4">{title}:</h2>}
          <button onClick={onClose} className="p-4">
            <Image
              src="/assets/close_icon.svg"
              alt="close icon"
              width={15}
              height={15}
            />
          </button>
        </div>
        <div>{children}</div>
      </div>
    </div>,
    document.body
  );
};
