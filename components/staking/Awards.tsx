"use client";

import { getCoin, isGreater<PERSON><PERSON><PERSON><PERSON> } from "@/utils";
import Image from "next/image";
import { useState } from "react";
import { ModalClaimableRewards } from "./ModalClaimableRewards";
import { ChainName } from "cosmos-kit";

interface ClaimableRewardsProps {
  amount: string;
  chainName: ChainName;
  onClaim: () => void;
  rewards: {
    total: string;
    byValidators: Array<{
      validatorAddress: string;
      amount: string;
    }>;
  };
  updateData: () => void;
}

export const ClaimableRewards = ({
  amount,
  chainName,
  onClaim,
  rewards,
  updateData,
}: ClaimableRewardsProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const coin = getCoin(chainName);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div className="bg-main-400 rounded-xl py-6 px-8 flex items-center justify-between w-full shadow-lg md:flex-row flex-col gap-2 text-center">
        <div className="flex flex-col items-center w-full">
          <div className="flex flex-row gap-1 justify-center">
            <Image
              src="/assets/celebration_icon.svg"
              alt="celebration icon"
              width={20}
              height={20}
            />
            <span className="text-main-100 text-sm">Claimable Rewards</span>
          </div>
          <p className="text-main-100 text-xl">
            {Number(rewards?.total || 0).toLocaleString()} {coin.symbol}
          </p>
        </div>
        <button
          onClick={openModal}
          disabled={!isGreaterThanZero(rewards?.total || "0")}
          className="bg-gradient-to-r from-[#00a8ff] to-[#cf08ee] text-white font-bold py-4 px-14 rounded-lg shadow-md transition hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Claim
        </button>
      </div>

      <ModalClaimableRewards
        isOpen={isModalOpen}
        onClose={closeModal}
        rewards={rewards}
        chainName={chainName}
        updateData={updateData}
      />
    </>
  );
};

export default function Awards() {
  // This is a placeholder implementation
  // In a real implementation, this would use the useStakingData hook
  const [mockRewards] = useState({
    total: "100",
    byValidators: [
      { validatorAddress: "empeval1...", amount: "60" },
      { validatorAddress: "empeval2...", amount: "40" },
    ],
  });

  const chainName = "empetestnet" as ChainName;

  const handleClaim = () => {
    // This would be handled by the modal in real implementation
    console.log("Claim button clicked");
  };

  const updateData = () => {
    // This would refetch data in real implementation
    console.log("Updating data");
  };

  return (
    <ClaimableRewards
      amount={mockRewards.total}
      chainName={chainName}
      onClaim={handleClaim}
      rewards={mockRewards}
      updateData={updateData}
    />
  );
}
