"use client";

import { memo } from "react";
import { ChainName } from "cosmos-kit";
import { getCoin } from "@/utils";
import { type ExtendedValidator as Validator } from "@/utils";
import { stakingColumnsAllValidators } from "../config";
import { ValidatorWithImage } from "../ValidatorWIthImage";
import { ButtonBasicSmall, TableBasic } from "@empe/front-kit-next-ui";
import { useDashboardStore } from "@/store/dashboardStore";
import { calculateVotingPower } from "@/utils/calculateVotingPower";
import { StakingUptime } from "@/types/stakingUptime";
import Link from "next/link";
import { useStakingModals } from "@/contexts/StakingModalsContext";
import { ValueWithSymbol } from "../ValueWithSymbol";
import { AmmountStaked } from "@/types/ammountStaked";
import { MobileTable } from "@/components/MobileTable/MobileTable";

const ValidatorsList = ({
  validators,
  uptime,
  openModal,
  chainName,
  logos,
  amountStaked,
}: {
  validators: Validator[];
  uptime: StakingUptime;
  chainName: ChainName;
  amountStaked: AmmountStaked[];
  openModal: (validator: Validator) => void;
  logos: { [key: string]: string };
}) => {
  const coin = getCoin(chainName);
  const { bondedTokens } = useDashboardStore();
  const { openValidatorDetailsSubpage } = useStakingModals();

  const data = validators.map((validator) => ({
    validator: {
      value: (
        <Link
          href={`/staking/validator-details/${validator.address}`}
          onClick={() => openValidatorDetailsSubpage(validator)}
          className="cursor-pointer"
        >
          <ValidatorWithImage
            validator={validator}
            logos={logos}
            staticRow={true}
          />
        </Link>
      ),
      sortKey: validator.name,
    },
    amount: {
      value: (
        <ValueWithSymbol
          value={
            amountStaked?.find(
              (item) => item.validatorAddress === validator.address
            )?.amount || "0"
          }
          symbol={coin.symbol}
        />
      ),
      sortKey: Number(
        (
          amountStaked?.find(
            (item) => item.validatorAddress === validator.address
          )?.amount || "0"
        ).replace(/,/g, "")
      ),
    },
    commission: validator.commission,
    uptime: uptime[validator.address]?.uptime || 0,
    reward: {
      value: <ValueWithSymbol value={validator.reward} symbol={coin.symbol} />,
      sortKey: Number(validator.reward),
    },
    votingPower: {
      value: calculateVotingPower(validator.votingPower, bondedTokens),
      sortKey: Number(validator.votingPower),
    },
    actions: (
      <div className="w-[120px] flex items-center justify-center mx-auto">
        <ButtonBasicSmall
          onClick={() => {
            openModal(validator);
          }}
        >
          Delegate
        </ButtonBasicSmall>
      </div>
    ),
  }));

  return (
    <>
      <span className="hidden md:block w-full overflow-x-hidden without-scrollbar">
        <TableBasic data={data} columns={stakingColumnsAllValidators} />
      </span>
      <span className="md:hidden">
        <MobileTable data={data} columns={stakingColumnsAllValidators} />
      </span>
    </>
  );
};

ValidatorsList.displayName = "ValidatorsList";

export default memo(ValidatorsList);
