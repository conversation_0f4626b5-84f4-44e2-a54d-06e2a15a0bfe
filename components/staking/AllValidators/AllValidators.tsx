"use client";

import { ChainName } from "cosmos-kit";
import AllValidatorsList from "./AllValidatorsList";
import { type ExtendedValidator as Validator } from "@/utils";
import { StakingUptime } from "@/types/stakingUptime";
import { useStakingModals } from "@/contexts/StakingModalsContext";
import { AmmountStaked } from "@/types/ammountStaked";

export const AllValidators = ({
  uptime,
  validators,
  chainName,
  logos,
  amountStaked,
}: {
  uptime: StakingUptime;
  validators: Validator[];
  chainName: ChainName;
  logos: {
    [key: string]: string;
  };
  amountStaked: AmmountStaked[] | undefined;
}) => {
  const { openDelegateModal } = useStakingModals();

  return (
    <div className="flex flex-col gap-2">
      <p className="text-main-600 text-xl">All Validators:</p>
      <AllValidatorsList
        uptime={uptime}
        validators={validators}
        chainName={chainName}
        logos={logos}
        amountStaked={amountStaked}
        openModal={(validator) => openDelegateModal(validator)}
      />
    </div>
  );
};
