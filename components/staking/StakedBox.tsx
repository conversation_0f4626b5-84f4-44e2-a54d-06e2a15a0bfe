import { getCoin } from "@/utils";
import Image from "next/image";

export const StakedBox = ({
  chainName,
  staked,
}: {
  staked: number;
  chainName: string;
}) => {
  const coin = getCoin(chainName);

  return (
    <div className="py-6 px-8 bg-main-400 rounded-xl shadow-lg mx-auto flex justify-center items-center flex-col gap-2 w-full">
      <div className="flex items-center gap-2 justify-center">
        <Image
          src="assets/lock_icon.svg"
          alt="lock icon"
          width={18}
          height={18}
        />
        <span className="text-main-600 text-sm">Staked:</span>
      </div>
      <div className="text-white text-xl flex flex-row gap-1">
        {Number(staked).toLocaleString()}
        <span className="text-main-800">{coin.symbol}</span>
      </div>
    </div>
  );
};
