export const stakingColumnsMyValidators = [
  { key: "validator", label: "Validator", sortable: true },
  {
    key: "amount",
    label: "Amount Staked",
    sortable: true,
  },
  {
    key: "commission",
    label: "Commission",
    sortable: true,
    format: (val: number) => `${(val * 100).toFixed(1)}%`,
  },
  {
    key: "uptime",
    label: "Uptime",
    sortable: true,
    format: (val: number) => `${val}%`,
  },
  {
    key: "reward",
    label: "Claimable Rewards",
    sortable: true,
  },
  {
    key: "votingPower",
    label: "Voting Power",
    sortable: true,
    format: (val: number) => `${(val * 100).toFixed(2)}%`,
  },
  { key: "actions", label: "", sortable: false },
];

export const stakingColumnsAllValidators = [
  { key: "validator", label: "Validator", sortable: true },
  {
    key: "amount",
    label: "Amount Staked",
    sortable: true,
  },
  {
    key: "commission",
    label: "Commission",
    sortable: true,
    format: (val: number) => `${(val * 100).toFixed(1)}%`,
  },
  {
    key: "uptime",
    label: "Uptime",
    sortable: true,
    format: (val: number) => `${val}%`,
  },
  {
    key: "reward",
    label: "Claimable Rewards",
    sortable: true,
  },
  {
    key: "votingPower",
    label: "Voting Power",
    sortable: true,
    format: (val: number) => `${(val * 100).toFixed(2)}%`,
  },
  { key: "actions", label: "", sortable: false },
];

export const stakingColumnsSelectValidator = [
  { key: "radio", label: "", sortable: false },
  { key: "validator", label: "Validator", sortable: true },
  {
    key: "commission",
    label: "Commission",
    sortable: true,
    format: (val: number) => `${(val * 100).toFixed(1)}%`,
  },
  {
    key: "uptime",
    label: "Uptime",
    sortable: true,
    format: (val: number) => `${val}%`,
  },
  { key: "reward", label: "Claimable Rewards", sortable: true },
  {
    key: "votingPower",
    label: "Voting Power",
    sortable: true,
    format: (val: number) => `${(val * 100).toFixed(2)}%`,
  },
];
