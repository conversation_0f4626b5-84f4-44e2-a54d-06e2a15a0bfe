"use client";

import { useState } from "react";
import { cosmos } from "@empe/empejs";
import { useChain } from "@cosmos-kit/react";
import { ChainName } from "cosmos-kit";
import BigNumber from "bignumber.js";
import {
  getAssetLogoUrl,
  isGreaterThanZero,
  toBaseAmount,
  type ExtendedValidator as Validator,
} from "@/utils";
import { getCoin, getExponent } from "@/utils";
import { Prices, UseDisclosureReturn, useTx } from "@/hooks";
import { BasicModal } from "./BasicModal";
import Image from "next/image";
import { useRouter } from "next/router";
import { StakingModalValidatorHeader } from "../StakingModalValidatorHeader";
import { CalculateStakingValue } from "./CalculateStakingValue";

const { beginRedelegate } = cosmos.staking.v1beta1.MessageComposer.fromPartial;

interface RedelegateModalProps {
  updateData: () => void;
  openValidatorDetailsSubpage: () => void;
  chainName: ChainName;
  selectedValidator: Validator;
  validatorToRedelegate: Validator;
  modalControl: UseDisclosureReturn;
  prices: Prices;
  logoUrl: string;
  balance: string;
}

export const RedelegateModal = ({
  updateData,
  openValidatorDetailsSubpage,
  chainName,
  modalControl,
  selectedValidator,
  validatorToRedelegate,
  logoUrl,
  balance,
}: RedelegateModalProps) => {
  const { address } = useChain(chainName);
  const router = useRouter();

  const [amount, setAmount] = useState<number | undefined>(0);
  const [inputValue, setInputValue] = useState<string>("");

  const coin = getCoin(chainName);
  const exp = getExponent(chainName);

  const { tx } = useTx(chainName);

  const closeRedelegateModal = () => {
    setAmount(0);
    modalControl.onClose();
  };

  const handleOpenValidatorDetailsSubpage = () => {
    openValidatorDetailsSubpage();
    closeRedelegateModal();
  };

  const onRedelegateClick = async () => {
    if (!address || !amount) return;

    const msg = beginRedelegate({
      delegatorAddress: address,
      validatorSrcAddress: selectedValidator.address,
      validatorDstAddress: validatorToRedelegate.address,
      amount: {
        denom: coin.base,
        amount: toBaseAmount(amount, exp),
      },
    });

    await tx([msg], {
      onSuccess: () => {
        updateData();
        closeRedelegateModal();
        router.replace("/staking");
      },
    });
  };

  return (
    <BasicModal
      title="Redelegate"
      isOpen={modalControl.isOpen}
      onClose={closeRedelegateModal}
    >
      <div className="md:max-w-4xl bg-main-400 flex flex-col gap-6 lg:p-8 p-2 text-white">
        <div className="overflow-x-auto md:overflow-hidden md:max-w-full">
          <StakingModalValidatorHeader
            onClose={handleOpenValidatorDetailsSubpage}
            selectedValidator={selectedValidator}
            logoUrl={logoUrl}
          />
          <div className="bg-main-1500 py-3 px-4 rounded-lg w-full md:w-1/2 mx-auto flex-1 justify-center items-center text-center">
            <p className="text-main-800 text-sm">
              {"Tokens Available for Redelegation:"}
            </p>
            <div className="flex flex-row gap-1 text-center justify-center items-center">
              <p className="text-main-600">{balance}</p>
              <p className="text-main-800">{coin.symbol}</p>
            </div>
          </div>
        </div>

        <CalculateStakingValue
          coin={coin}
          inputValue={inputValue}
          inputOnChange={(e) => {
            let value = e.target.value.replace(/\,/g, ".");
            value = value.replace(/[^0-9,]/g, ".");

            setInputValue(value);

            const numericValue = parseFloat(value.replace(",", "."));
            setAmount(isNaN(numericValue) ? 0 : numericValue);
          }}
          buttonFirstOnClick={() => {
            if (selectedValidator.delegation) {
              const newAmount = new BigNumber(selectedValidator.delegation)
                .dividedBy(2)
                .toNumber();
              setAmount(newAmount);
              setInputValue(newAmount.toString().replace(",", "."));
            }
          }}
          buttonSecondOnClick={() => {
            if (selectedValidator.delegation) {
              const newAmount = new BigNumber(selectedValidator.delegation)
                .dividedBy(3)
                .toNumber();
              setAmount(newAmount);
              setInputValue(newAmount.toString().replace(",", "."));
            }
          }}
          buttonMaxOnClick={() => {
            if (selectedValidator.delegation) {
              setAmount(Number(selectedValidator.delegation));
              setInputValue(
                selectedValidator.delegation.toString().replace(",", ".")
              );
            }
          }}
        />

        <button
          className={`py-2 px-4 text-white mx-auto w-1/2 self-center font-semibold rounded-lg shadow-md transition-all ${
            isGreaterThanZero(amount)
              ? "bg-gradient-to-r from-[#00AFFF] via-[#006EFF] to-[#E200ED] hover:from-[#0099E0] hover:via-[#005BCC] hover:to-[#C700D6] "
              : "bg-[#373737] cursor-not-allowed "
          }`}
          onClick={onRedelegateClick}
          disabled={!isGreaterThanZero(amount)}
        >
          Redelegate
        </button>
      </div>
    </BasicModal>
  );
};
