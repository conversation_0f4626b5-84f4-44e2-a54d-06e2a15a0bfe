"use client";

import React, { useState, useEffect } from "react";
import { cosmos } from "@empe/empejs";
import { StdFee } from "@cosmjs/amino";
import { useChain } from "@cosmos-kit/react";
import { ChainName } from "cosmos-kit";
import BigNumber from "bignumber.js";
import {
  type ExtendedValidator as Validator,
  isGreater<PERSON>hanZero,
  shiftDigits,
  getCoin,
  getExponent,
  toBaseAmount,
} from "@/utils";
import { Prices, UseDisclosureReturn, useTx } from "@/hooks";
import { BasicModal } from "./BasicModal";
import { StakingModalValidatorHeader } from "../StakingModalValidatorHeader";
import { Checkbox } from "../common/Checkbox";
import { CalculateStakingValue } from "./CalculateStakingValue";

const { delegate } = cosmos.staking.v1beta1.MessageComposer.fromPartial;

export type MaxAmountAndFee = {
  maxAmount: number;
  fee: StdFee;
};

interface Props {
  balance: string;
  updateData: () => void;
  unbondingDays: string;
  chainName: ChainName;
  modalControl: UseDisclosureReturn;
  selectedValidator: Validator;
  logoUrl: string;
  prices: Prices;
  closeOuterModal?: () => void;
  openValidatorDetailsSubpage: () => void;
  modalTitle?: string;
  showDescription?: boolean;
}

export const DelegateModal = ({
  balance,
  updateData,
  unbondingDays,
  chainName,
  logoUrl,
  modalControl,
  selectedValidator,
  closeOuterModal,
  modalTitle,
  openValidatorDetailsSubpage,
}: Props) => {
  const { isOpen, onClose } = modalControl;
  const { address, estimateFee } = useChain(chainName);

  const [amount, setAmount] = useState<number | undefined>(0);
  const [isDelegating, setIsDelegating] = useState(false);
  const [isSimulating, setIsSimulating] = useState(false);
  const [maxAmountAndFee, setMaxAmountAndFee] = useState<MaxAmountAndFee>();
  const [inputValue, setInputValue] = useState<string>("0");
  const [error, setError] = useState<string | null>(null);
  const [checked, setChecked] = useState(false);

  const coin = getCoin(chainName);
  const exp = getExponent(chainName);
  const { tx } = useTx(chainName);

  const onModalClose = () => {
    onClose();
    setAmount(0);
    setInputValue("0");
    setIsDelegating(false);
    setIsSimulating(false);
    setError(null);
  };

  // Validate amount whenever it changes
  useEffect(() => {
    if (!amount) return;

    if (new BigNumber(amount).isGreaterThan(balance)) {
      setError("Amount exceeds available balance");
    } else {
      setError(null);
    }
  }, [amount, balance]);

  const onDelegateClick = async () => {
    if (!address || !amount) return;
    if (error) return;

    setIsDelegating(true);
    setError(null);

    try {
      const msg = delegate({
        delegatorAddress: address,
        validatorAddress: selectedValidator.address,
        amount: {
          amount: toBaseAmount(amount, exp),
          denom: coin.base,
        },
      });

      const isMaxAmountAndFeeExists =
        maxAmountAndFee &&
        new BigNumber(amount).isEqualTo(maxAmountAndFee.maxAmount);

      await tx([msg], {
        fee: isMaxAmountAndFeeExists ? maxAmountAndFee.fee : null,
        onSuccess: () => {
          setMaxAmountAndFee(undefined);
          closeOuterModal && closeOuterModal();
          updateData();
          onModalClose();
        },
      });
    } catch (err) {
      console.error("Error preparing delegation:", err);
      setError("Failed to prepare transaction. Please try again.");
    } finally {
      setIsDelegating(false);
    }
  };

  const handleMaxClick = async () => {
    if (!address) return;

    if (Number(balance) === 0) {
      setAmount(0);
      setInputValue("0");
      return;
    }

    setIsSimulating(true);
    setError(null);

    try {
      const msg = delegate({
        delegatorAddress: address,
        validatorAddress: selectedValidator.address,
        amount: {
          amount: shiftDigits(balance, exp),
          denom: coin.base,
        },
      });

      const fee = await estimateFee([msg]);
      const feeAmount = new BigNumber(fee.amount[0].amount).shiftedBy(-exp);
      const balanceAfterFee = new BigNumber(balance)
        .minus(feeAmount)
        .toNumber();

      if (balanceAfterFee <= 0) {
        setError("Balance too low to cover transaction fees");
        setAmount(0);
        setInputValue("0");
      } else {
        setMaxAmountAndFee({ fee, maxAmount: balanceAfterFee });
        setAmount(balanceAfterFee);
        setInputValue(balanceAfterFee.toString());
      }
    } catch (error) {
      console.error("Error estimating fee:", error);
      setError("Failed to estimate transaction fee");
    } finally {
      setIsSimulating(false);
    }
  };

  const handleOpenValidatorDetailsSubpage = () => {
    openValidatorDetailsSubpage();
    onClose();
  };

  return (
    <BasicModal
      title={modalTitle || "Delegate"}
      isOpen={isOpen}
      onClose={onModalClose}
    >
      <div className="max-w-4xl bg-main-400 flex flex-col text-main-600 lg:gap-6 gap-4 lg:p-8 p-2">
        <StakingModalValidatorHeader
          onClose={handleOpenValidatorDetailsSubpage}
          selectedValidator={selectedValidator}
          logoUrl={logoUrl}
        />
        <div className="border-y border-white/10 py-6">
          <Checkbox
            checked={checked}
            onChange={setChecked}
            label="I understand undelegate process will take 21 days for EMPE to become liquid upon withdrawal."
            disabled={isGreaterThanZero(balance) === false}
          />
        </div>
        <div className="flex flex-row gap-6 w-full md:w-1/2 mx-auto">
          <div className="bg-main-1500 py-3 px-4 rounded-lg flex-1 justify-center items-center text-center">
            <p className="text-main-800 text-sm">
              {"Tokens Available for Staking:"}
            </p>
            <div className="flex flex-row gap-1 text-center justify-center items-center">
              <p className="text-main-600">{balance}</p>
              <p className="text-main-800">{coin.symbol}</p>
            </div>
          </div>
        </div>

        <CalculateStakingValue
          coin={coin}
          error={error}
          inputValue={inputValue}
          isSimulating={isSimulating}
          inputOnChange={(e) => {
            let value = e.target.value.replace(/,/g, ".");
            value = value.replace(/[^0-9.]/g, "");

            setInputValue(value);

            const numericValue = parseFloat(value);
            setAmount(isNaN(numericValue) ? 0 : numericValue);
          }}
          buttonFirstOnClick={() => {
            if (balance) {
              const newAmount = new BigNumber(balance).dividedBy(2).toNumber();
              setAmount(newAmount);
              setInputValue(newAmount.toString());
            }
          }}
          buttonSecondOnClick={() => {
            if (balance) {
              const newAmount = new BigNumber(balance).dividedBy(3).toNumber();
              setAmount(newAmount);
              setInputValue(newAmount.toString());
            }
          }}
          buttonMaxOnClick={handleMaxClick}
        />

        <button
          className={`py-2 px-4 text-white font-semibold mx-auto w-1/2 rounded-lg shadow-md transition-all ${
            isGreaterThanZero(amount) &&
            !isDelegating &&
            !isSimulating &&
            !error &&
            checked
              ? "bg-gradient-to-r from-[#00AFFF] via-[#006EFF] to-[#E200ED] hover:from-[#0099E0] hover:via-[#005BCC] hover:to-[#C700D6]"
              : "bg-[#373737] cursor-not-allowed !text-[#9b9c9e]"
          }`}
          onClick={onDelegateClick}
          disabled={
            !isGreaterThanZero(amount) ||
            isDelegating ||
            isSimulating ||
            !!error ||
            !checked
          }
        >
          {isDelegating ? "Processing..." : "Delegate"}
        </button>
      </div>
    </BasicModal>
  );
};
