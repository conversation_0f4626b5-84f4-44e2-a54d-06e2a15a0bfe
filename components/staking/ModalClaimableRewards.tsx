"use client";

import { useState } from "react";
import { use<PERSON>hain } from "@cosmos-kit/react";
import { ChainName } from "cosmos-kit";
import { BasicModal } from "./BasicModal";
import { getCoin, isGreaterThanZero } from "@/utils";
import { useTx } from "@/hooks/common/useTx";
import { cosmos } from "@empe/empejs";
import Image from "next/image";

const { withdrawDelegatorReward } =
  cosmos.distribution.v1beta1.MessageComposer.fromPartial;

interface ModalClaimableRewardsProps {
  isOpen: boolean;
  onClose: () => void;
  rewards: {
    total: string;
    byValidators: Array<{
      validatorAddress: string;
      amount: string;
    }>;
  };
  chainName: ChainName;
  updateData: () => void;
}

export const ModalClaimableRewards = ({
  isOpen,
  onClose,
  rewards,
  chainName,
  updateData,
}: ModalClaimableRewardsProps) => {
  const [isClaiming, setIsClaiming] = useState(false);
  const { address } = useChain(chainName);
  const { tx } = useTx(chainName);
  const coin = getCoin(chainName);

  const onClaimRewardClick = async () => {
    setIsClaiming(true);

    if (!address) {
      setIsClaiming(false);
      return;
    }

    const msgs = rewards.byValidators.map(({ validatorAddress }) =>
      withdrawDelegatorReward({
        delegatorAddress: address,
        validatorAddress,
      })
    );

    await tx(msgs, {
      onSuccess: () => {
        updateData();
        onClose();
      },
      toast: {
        title: "Rewards Claimed",
        description: `Successfully claimed ${rewards.total} ${coin.symbol}`,
        type: "success",
      },
    });

    setIsClaiming(false);
  };

  return (
    <BasicModal isOpen={isOpen} onClose={onClose} title="Claim Rewards">
      <div className="py-6 text-white">
        <div className="flex flex-col items-center mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Image
              src="/assets/celebration_icon.svg"
              alt="celebration icon"
              width={30}
              height={30}
            />
            <h3 className="text-xl font-semibold">Claimable Rewards</h3>
          </div>

          <p className="text-3xl font-bold mb-2">
            {Number(rewards?.total || 0).toLocaleString()}{" "}
            <span className="text-main-800">{coin.symbol}</span>
          </p>

          <p className="text-sm text-main-800 mb-6">
            You have rewards from {rewards.byValidators.length} validator
            {rewards.byValidators.length !== 1 ? "s" : ""}
          </p>

          <button
            onClick={onClaimRewardClick}
            disabled={isClaiming || !isGreaterThanZero(rewards?.total || "0")}
            className="bg-gradient-to-r from-[#00a8ff] to-[#cf08ee] text-white font-bold py-4 px-14 rounded-lg shadow-md transition hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed w-full max-w-xs"
          >
            {isClaiming ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Claiming...
              </div>
            ) : (
              "Claim Rewards"
            )}
          </button>
        </div>
      </div>
    </BasicModal>
  );
};
