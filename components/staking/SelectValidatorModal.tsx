"use client";

import { useMemo, useState } from "react";
import { ChainName } from "cosmos-kit";
import { Text, GridColumn, ValidatorList } from "@interchain-ui/react";
import { getCoin } from "@/utils";
import { UseDisclosureReturn } from "@/hooks";
import { shiftDigits, type ExtendedValidator as Validator } from "@/utils";
import { BasicModal } from "./BasicModal";
import { ImageWithOnError } from "../ImageWithOnError";

export const SelectValidatorModal = ({
  allValidators,
  chainName,
  logos,
  handleValidatorClick,
  modalControl,
}: {
  allValidators: Validator[];
  chainName: ChainName;
  handleValidatorClick: (validator: Validator) => void;
  modalControl: UseDisclosureReturn;
  logos: {
    [key: string]: string;
  };
}) => {
  const coin = getCoin(chainName);
  const [selectedValidator, setSelectedValidator] = useState<Validator | null>(
    null
  );

  const columns = useMemo(() => {
    const hasApr = !!allValidators[0]?.apr;

    const _columns: GridColumn[] = [
      {
        id: "validator",
        label: "Validator",
        width: "300px",
        align: "left",
        color: "#909093",
        render: (validator: Validator) => {
          return (
            <div className="flex items-center gap-3">
              <ImageWithOnError src={logos[validator.address]} />
              <p className="text-lg font-normal">{validator.name}</p>
            </div>
          );
        },
      },
      // {
      //   id: "amount-staked",
      //   label: "Amount Staked",
      //   width: "200px",
      //   align: "center",
      //   color: "#909093",
      //   render: (validator: Validator) => (
      //     <div className="flex flex-row gap-1 justify-center items-center">
      //       <p className="text-lg font-normal">{validator.votingPower}</p>
      //       <p className="text-lg font-normal text-main-800"> {coin.symbol}</p>
      //     </div>
      //   ),
      // },
      {
        id: "voting-power",
        label: "Voting Power",
        width: "80px",
        align: "center",
        color: "#909093",
        render: (validator: Validator) => (
          <div className="flex flex-row gap-1 justify-center items-center">
            <p className="text-lg font-normal">{validator.votingPower}</p>
            <p className="text-lg font-normal text-main-800"> {coin.symbol}</p>
          </div>
        ),
      },
      // {
      //   id: "uptime",
      //   label: "Uptime",
      //   width: "120px",
      //   align: "center",
      //   color: "#909093",
      //   render: (validator: Validator) => (
      //     <div>{shiftDigits(validator.commission, 2)}%</div>
      //   ),
      // },
      {
        id: "commission",
        label: "Commission",
        width: "160px",
        align: "center",
        color: "#909093",
        render: (validator: Validator) => (
          <div>{shiftDigits(validator.commission, 2)}%</div>
        ),
      },
      {
        id: "action",
        width: "126px",
        align: "right",
        render: (validator) => (
          <button
            onClick={() => handleValidatorClick(validator)}
            className="px-4 py-2 text-white border border-white rounded-md"
          >
            Select
          </button>
        ),
      },
    ];

    if (hasApr) {
      _columns.splice(3, 0, {
        id: "apr",
        label: "APR",
        width: "106px",
        align: "right",
        render: (validator: Validator) => (
          <Text fontWeight="$semibold">{validator.apr}%</Text>
        ),
      });
    }

    return _columns;
  }, [chainName]);

  return (
    <BasicModal
      title="Redelegate to"
      isOpen={modalControl.isOpen}
      onClose={modalControl.onClose}
    >
      <div
        className="bg-main-400 flex flex-col gap-6 p-8 text-white without-scrollbar"
        style={{ overflowY: "scroll" }}
      >
        <div className="max-h-[550px] validator-list scroll-smooth">
          <ValidatorList
            columns={columns}
            data={allValidators}
            tableProps={{ width: "$full" }}
            variant="ghost"
          />
        </div>
      </div>
    </BasicModal>
  );
};
