"use client";
import { getCoin } from "@/utils";
import { ChainName } from "cosmos-kit";
import { type ExtendedValidator as Validator } from "@/utils";
import { UseDisclosureReturn } from "@/hooks";
import { BasicModal } from "../BasicModal";
import { DelegationInfo } from "./DelegationInfo";
import { ActionButtons } from "./ActionButtons";
import { StakingModalValidatorHeader } from "@/components/StakingModalValidatorHeader";

interface Props {
  chainName: ChainName;
  modalControl: UseDisclosureReturn;
  selectedValidator: Validator;
  handleClick: {
    openDelegateModal: () => void;
    openUndelegateModal: () => void;
    openSelectValidatorModal: () => void;
    openValidatorDetailsSubpage: () => void;
  };
  logoUrl: string;
}

export const ValidatorInfoModal = ({
  chainName,
  logoUrl,
  handleClick,
  modalControl,
  selectedValidator,
}: Props) => {
  const coin = getCoin(chainName);
  const { isOpen, onClose } = modalControl;
  const {
    openDelegateModal,
    openSelectValidatorModal,
    openUndelegateModal,
    openValidatorDetailsSubpage,
  } = handleClick;

  const handleOpenValidatorDetailsSubpage = () => {
    openValidatorDetailsSubpage();
    onClose();
  };

  return (
    <BasicModal title="Validator" isOpen={isOpen} onClose={onClose}>
      <div className="max-w-4xl bg-main-400 text-main-600 flex flex-col md:gap-6 gap-4 md:py-6 md:px-8 py-2 px-4">
        <StakingModalValidatorHeader
          onClose={handleOpenValidatorDetailsSubpage}
          selectedValidator={selectedValidator}
          logoUrl={logoUrl}
        />
        <ActionButtons
          {...{
            onClose,
            openUndelegateModal,
            openSelectValidatorModal,
            openDelegateModal,
          }}
        />
      </div>
    </BasicModal>
  );
};
