import Link from "next/link";

export const ActionButtons = ({
  onClose,
  openUndelegateModal,
  openDelegateModal,
}: {
  onClose: () => void;
  openUndelegateModal: () => void;
  openSelectValidatorModal: () => void;
  openDelegateModal: () => void;
}) => (
  <div className="flex md:flex-row flex-col gap-4 md:gap-6 justify-between items-center w-full md:px-10 pb-6">
    <button
      className="border border-white text-white py-3 px-8 rounded-lg w-full md:flex-1"
      onClick={() => {
        onClose();
        openUndelegateModal();
      }}
    >
      Undelegate
    </button>
    <Link
      href={"/staking/select-validator"}
      className="border border-white text-white text-center py-3 px-8 rounded-lg w-full md:flex-1"
      onClick={() => {
        onClose();
      }}
    >
      Redelegate
    </Link>
    <button
      className="py-3 px-8 text-white font-semibold rounded-lg shadow-md transition-all w-full md:flex-1
        bg-gradient-to-r from-[#00AFFF] via-[#006EFF] to-[#E200ED] hover:from-[#0099E0] hover:via-[#005BCC] hover:to-[#C700D6]"
      onClick={() => {
        onClose();
        openDelegateModal();
      }}
    >
      Delegate
    </button>
  </div>
);
