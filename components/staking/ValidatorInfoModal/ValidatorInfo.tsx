import {
  formatValidatorMetaInfo,
  type ExtendedValidator as Validator,
} from "@/utils";
import Image from "next/image";

export const ValidatorInfo = ({
  logoUrl,
  selectedValidator,
}: {
  logoUrl: string;
  selectedValidator: Validator;
}) => (
  <div className="flex flex-row items-center gap-4 py-3 px-4 flex-1">
    <Image
      src={logoUrl || "/assets/modal_image.png"}
      alt="modal image"
      width={64}
      height={64}
    />
    <div className="flex flex-col gap-2 justify-center">
      <p className="text-white text-xl">{selectedValidator.name}</p>
      <p className="text-main-800 text-sm">
        {formatValidatorMetaInfo(selectedValidator)}
      </p>
    </div>
  </div>
);
