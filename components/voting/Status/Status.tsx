import Image from "next/image";
import { Proposal as ProposalType } from "interchain-query/cosmos/gov/v1/gov";
import { percent } from "@/utils";
import { ProposalStatus } from "interchain-query/cosmos/gov/v1/gov";

interface Props {
  turnoutPercentage: string;
  proposal: ProposalType;
  total: number;
}

const ProposalStatusLabel = {
  [ProposalStatus.PROPOSAL_STATUS_PASSED]: "Passed",
  [ProposalStatus.PROPOSAL_STATUS_REJECTED]: "Rejected",
  [ProposalStatus.PROPOSAL_STATUS_FAILED]: "Failed",
  [ProposalStatus.PROPOSAL_STATUS_DEPOSIT_PERIOD]: "Deposit Period",
  [ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD]: "Voting Period",
  [ProposalStatus.PROPOSAL_STATUS_UNSPECIFIED]: "Unspecified",
  [ProposalStatus.UNRECOGNIZED]: "Unrecognized",
};

export const Status = ({ turnoutPercentage, proposal, total }: Props) => {
  return (
    <div className="flex flex-row md:flex-col self-center gap-2 md:gap-8 w-full items-center justify-center flex-1">
      <div className="bg-main-1100 rounded-xl flex items-center flex-row gap-2 md:gap-4 p-4 md:max-w-[200px] justify-center w-full">
        {/* TODO: icon equals proposal status */}
        <Image src="/assets/status_passed.svg" alt="" width={38} height={38} />
        <div className="flex flex-col items-center justify-center">
          <p className="text-lg">{ProposalStatusLabel[proposal.status]}</p>
          {/* <p className="text-center">
            {total > 0
              ? `${Math.max(
                  percent(proposal.finalTallyResult?.yesCount, total),
                  percent(proposal.finalTallyResult?.abstainCount, total),
                  percent(proposal.finalTallyResult?.noCount, total),
                  percent(proposal.finalTallyResult?.noWithVetoCount, total)
                ).toFixed(2)}%`
              : "No tally results available"}
          </p> */}
        </div>
      </div>
      <div className="bg-main-1100 rounded-xl flex items-center flex-row gap-2 md:gap-4 p-4 md:max-w-[200px] justify-center w-full">
        <Image
          alt=""
          src="/assets/turnout_proposal.svg"
          width={40}
          height={40}
        />
        <div className="flex flex-col items-center justify-center">
          <p className="text-lg">Turnout</p>
          <p>{turnoutPercentage}%</p>
        </div>
      </div>
    </div>
  );
};
