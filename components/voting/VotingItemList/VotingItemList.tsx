import Image from "next/image";
import {
  Proposal as IProposal,
  TallyResult,
  ProposalStatus,
} from "interchain-query/cosmos/gov/v1/gov";
import { formatDate } from "@/utils";
import { VotesBar } from "../VotesBar";
import { getStatus, statusIcons, statusColors } from "../VotingStatus";
import { useEffect, useState } from "react";
import { useProposalMetadata } from "@/hooks/voting/useProposalMetadata";

type Props = {
  proposal: IProposal;
  tally?: TallyResult;
  hasVoted: boolean;
  onClick: () => void;
};

const getWinningVoteColor = (tally?: TallyResult) => {
  if (!tally) return "bg-gray-500";

  const yes = Number(tally.yesCount) || 0;
  const no = Number(tally.noCount) || 0;
  const abstain = Number(tally.abstainCount) || 0;
  const noWithVeto = Number(tally.noWithVetoCount) || 0;
  const total = yes + no + abstain + noWithVeto;

  if (total === 0) return "bg-gray-500";

  if (noWithVeto > 0) return "bg-amber-500";

  const max = Math.max(yes, no, abstain);
  if (max === yes) return "bg-green-500";
  if (max === no) return "bg-red-500";
  return "bg-main-100";
};

export const VotingItemList = ({ proposal, hasVoted, onClick }: Props) => {
  const proposalStatus = getStatus(proposal.status);
  const winningColor = getWinningVoteColor(proposal.finalTallyResult);
  const { proposalTitle } = useProposalMetadata({ proposal });

  return (
    <div className="relative flex flex-col gap-6 my-6">
      {/* {hasVoted ? (
        <p className={`${winningColor} font-semibold text-lg`}>Voted</p>
      ) : proposal.status === ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD ? (
        <p className="font-semibold text-lg">Vote</p>
      ) : null} */}
      <div
        className="bg-main-400 flex flex-col md:flex-row py-2 px-4 justify-between items-center gap-4 rounded-lg cursor-pointer"
        onClick={onClick}
      >
        <div className="flex flex-row gap-2 min-w-[100px]">
          <Image
            src={statusIcons[proposalStatus]}
            alt={proposalStatus}
            width={20}
            height={20}
          />
          <p>{proposalStatus}</p>
        </div>
        <div
          className="border-r border-main-1100 self-stretch"
          style={{ borderWidth: "0.5px" }}
        ></div>
        <div className="flex flex-col gap-2 w-full items-start justify-start">
          <p className="text-lg font-semibold">{proposalTitle}</p>
          <div className="bg-main-900 text-sm px-2 rounded-md">
            <p>{`#${proposal.id?.toString()}`}</p>
          </div>
          <VotesBar tally={proposal.finalTallyResult} />
        </div>
        <div
          className="border-r border-main-1100 self-stretch"
          style={{ borderWidth: "0.5px" }}
        ></div>
        <div className="flex flex-row md:flex-col min-w-48 gap-2 justify-start items-start">
          <div
            className={`${winningColor} text-xs text-white font-semibold rounded-lg px-2 py-1text-center`}
          >
            {hasVoted ||
            proposal.status !== ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD
              ? "Voted"
              : "Vote"}
          </div>
          <div className="flex flex-col gap-1">
            <p className="text-xs">Voting end time</p>
            <p className="text-xs">{formatDate(proposal.votingEndTime)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};
