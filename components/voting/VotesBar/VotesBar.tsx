import { TallyResult } from "interchain-query/cosmos/gov/v1/gov";

export const VotesBar = ({ tally }: { tally?: TallyResult }) => {
  if (!tally) {
    return <p className="text-xs text-gray-400">No votes yet</p>;
  }

  const yes = Number(tally.yesCount) || 0;
  const no = Number(tally.noCount) || 0;
  const abstain = Number(tally.abstainCount) || 0;
  const noWithVeto = Number(tally.noWithVetoCount) || 0;
  const total = yes + no + abstain + noWithVeto;

  if (total === 0) {
    return <p className="text-xs text-gray-400">No votes yet</p>;
  }

  return (
    <div className="space-y-2 w-full py-2">
      <div className="w-full h-1 rounded-full overflow-hidden flex bg-gray-100">
        <div
          className="h-full bg-green-500 transition-all"
          style={{
            width: `${(yes / total) * 100}%`,
          }}
        ></div>
        <div
          className="h-full bg-red-500 transition-all"
          style={{
            width: `${(no / total) * 100}%`,
          }}
        ></div>
        <div
          className="h-full bg-main-100 transition-all"
          style={{
            width: `${(abstain / total) * 100}%`,
          }}
        ></div>
        <div
          className="h-full bg-amber-500 transition-all"
          style={{
            width: `${(noWithVeto / total) * 100}%`,
          }}
        ></div>
      </div>
    </div>
  );
};
