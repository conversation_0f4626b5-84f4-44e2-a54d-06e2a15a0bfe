import { ProposalStatus } from "interchain-query/cosmos/gov/v1/gov";

export const getStatus = (s: ProposalStatus): string => {
  switch (s) {
    case ProposalStatus.PROPOSAL_STATUS_UNSPECIFIED:
    case ProposalStatus.PROPOSAL_STATUS_DEPOSIT_PERIOD:
    case ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD:
      return "Pending";
    case ProposalStatus.PROPOSAL_STATUS_PASSED:
      return "Passed";
    case ProposalStatus.PROPOSAL_STATUS_REJECTED:
    case ProposalStatus.PROPOSAL_STATUS_FAILED:
      return "Rejected";
    default:
      return "Pending";
  }
};

export const statusIcons: Record<string, string> = {
  Pending: "/assets/status_pending.svg",
  Passed: "/assets/status_passed.svg",
  Rejected: "/assets/status_rejected.svg",
};

export const statusColors: Record<string, string> = {
  Pending: "bg-alert-200",
  Passed: "bg-alert-100",
  Rejected: "bg-alert-300",
  Veto: "bg-alert-400",
};
