import { useEffect, useState } from "react";
import { useVotingData } from "@/hooks";
import {
  Proposal as IProposal,
  ProposalStatus,
  TallyResult,
} from "interchain-query/cosmos/gov/v1/gov";
import { VotingItemList } from "../VotingItemList";

export type Props = {
  chainName: string;
  onSelectProposal: (proposal: IProposal) => void;
};

export const VotingList = ({ chainName, onSelectProposal }: Props) => {
  const { data, isLoading } = useVotingData(chainName);
  const [tallies, setTallies] = useState<{ [key: string]: TallyResult }>({});

  // Debug logging
  console.log(`VotingList for ${chainName}:`, {
    isLoading,
    proposalsCount: data?.proposals?.length || 0,
    proposals:
      data?.proposals?.map((p) => ({
        id: p.id,
        status: p.status,
        votingEndTime: p.votingEndTime,
        title: p.title,
      })) || [],
  });

  useEffect(() => {
    if (!data?.proposals || data.proposals.length === 0) return;
    data.proposals.forEach((proposal: IProposal) => {
      if (proposal.status === ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD) {
        fetchTally(proposal);
      }
    });
  }, [data?.proposals?.length, chainName]);

  async function fetchTally(proposal: IProposal) {
    try {
      const response = await fetch(
        `/cosmos/gov/v1/proposals/${String(proposal.id)}/tally`
      );
      const tally = await response.json();
      if (tally) {
        setTallies((prev) => ({
          ...prev,
          [proposal.id.toString()]: tally.tally,
        }));
      }
    } catch (e) {}
  }

  if (isLoading) {
    return <p>Loading proposals...</p>;
  }

  if (!data.proposals?.length) {
    return <p className="text-lg text-white text-center">No proposals found</p>;
  }

  const currentDate = new Date();

  const availableProposals = data.proposals.filter(
    (proposal: IProposal) => new Date(proposal.votingEndTime) > currentDate
  );

  const passedProposals = data.proposals.filter(
    (proposal: IProposal) =>
      new Date(proposal.votingEndTime) <= currentDate &&
      proposal.status === ProposalStatus.PROPOSAL_STATUS_PASSED
  );

  const rejectedProposals = data.proposals.filter(
    (proposal: IProposal) =>
      new Date(proposal.votingEndTime) <= currentDate &&
      proposal.status === ProposalStatus.PROPOSAL_STATUS_REJECTED
  );

  return (
    <div className="w-full">
      {availableProposals.length === 0 &&
      passedProposals.length === 0 &&
      rejectedProposals.length === 0 ? (
        <p className="text-lg text-white text-center">No proposals found</p>
      ) : (
        <>
          <section>
            <h2 className="text-xl py-8">Available Proposals:</h2>
            {availableProposals.length > 0 ? (
              availableProposals.map((proposal: IProposal) => (
                <VotingItemList
                  key={proposal.id?.toString()}
                  proposal={proposal}
                  tally={
                    tallies[proposal.id.toString()] || proposal.finalTallyResult
                  }
                  hasVoted={Boolean(data?.votes?.[proposal.id.toString()])}
                  onClick={() => onSelectProposal(proposal)}
                />
              ))
            ) : (
              <p className="text-center py-16">No available proposals</p>
            )}
          </section>

          <section>
            <h2 className="text-xl pt-4">Passed Proposals:</h2>
            {passedProposals.length > 0 ? (
              passedProposals.map((proposal: IProposal) => (
                <VotingItemList
                  key={proposal.id?.toString()}
                  proposal={proposal}
                  tally={
                    tallies[proposal.id.toString()] || proposal.finalTallyResult
                  }
                  hasVoted={Boolean(data?.votes?.[proposal.id.toString()])}
                  onClick={() => onSelectProposal(proposal)}
                />
              ))
            ) : (
              <p className="text-center">No passed proposals</p>
            )}
          </section>

          <section>
            <h2 className="text-xl pt-4">Rejected Proposals:</h2>
            {rejectedProposals.length > 0 ? (
              rejectedProposals.map((proposal: IProposal) => (
                <VotingItemList
                  key={proposal.id?.toString()}
                  proposal={proposal}
                  tally={
                    tallies[proposal.id.toString()] || proposal.finalTallyResult
                  }
                  hasVoted={Boolean(data?.votes?.[proposal.id.toString()])}
                  onClick={() => onSelectProposal(proposal)}
                />
              ))
            ) : (
              <p>No rejected proposals</p>
            )}
          </section>
        </>
      )}
    </div>
  );
};
