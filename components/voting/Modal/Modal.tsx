"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { formatDateTime } from "@/utils/formatDateTime";

export interface Props {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  metadata?: string;
  children?: React.ReactNode;
  id?: string;
  submitTime?: string;
  votingStartTime?: string;
  votingEndTime?: string;
}

export const Modal = ({
  isOpen,
  onClose,
  title,
  metadata,
  children,
  id,
  submitTime,
  votingStartTime,
  votingEndTime,
}: Props) => {
  const [proposalTitle, setProposalTitle] = useState<string>("Loading...");

  useEffect(() => {
    try {
      if (metadata) {
        const parsedMetadata = JSON.parse(metadata);
        if (parsedMetadata?.title) {
          setProposalTitle(parsedMetadata.title);
          return;
        }
      }

      setProposalTitle(title || `Proposal #${id || ""}`);
    } catch (error) {
      console.error("Error parsing metadata:", error);
      setProposalTitle(title || `Proposal #${id || ""}`);
    }
  }, [metadata, title, id]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return createPortal(
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="relative bg-main-400 max-h-[95vh] overflow-hidden rounded-2xl shadow-lg w-full max-w-4xl flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Static Header */}
        <div className="flex flex-row justify-between items-center py-6 px-8 flex-shrink-0">
          <div className="flex flex-row gap-2 items-center">
            <p className="text-white bg-main-1100 px-2 py-1 rounded">{`#${id}`}</p>
            {proposalTitle && (
              <h2 className="text-lg hidden md:block text-white whitespace-normal break-words">
                {proposalTitle}
              </h2>
            )}
          </div>

          <button onClick={onClose} className="ml-auto">
            <Image
              src="/assets/close_icon.svg"
              alt="close icon"
              width={15}
              height={15}
            />
          </button>
        </div>

        {/* Scrollable Content */}
        <div className="flex flex-1 px-1 flex-col mx-8 mb-6 overflow-y-auto without-scrollbar">
          {proposalTitle && (
            <h2 className="text-lg block md:hidden pb-4 text-white whitespace-normal break-words">
              {proposalTitle}
            </h2>
          )}
          <div className="bg-main-1500/70 rounded-lg px-4 md:px-8 py-6">
            <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-0 items-start md:items-center">
              <div className="flex flex-col">
                <p className="text-gray-400 mb-1">Submit Time</p>
                {submitTime &&
                  (() => {
                    const { date, time } = formatDateTime(submitTime);
                    return (
                      <div className="flex flex-row md:flex-col gap-1">
                        <p className="text-white">{date}</p>
                        <p className="text-white">{time}</p>
                      </div>
                    );
                  })()}
              </div>
              <div className="flex flex-col">
                <p className="text-gray-400 mb-1">Voting Starts</p>
                {votingStartTime &&
                  (() => {
                    const { date, time } = formatDateTime(votingStartTime);
                    return (
                      <div className="flex flex-row md:flex-col gap-1">
                        <p className="text-white">{date}</p>
                        <p className="text-white">{time}</p>
                      </div>
                    );
                  })()}
              </div>
              <div className="flex flex-col">
                <p className="text-gray-400 mb-1">Voting Ends</p>
                {votingEndTime &&
                  (() => {
                    const { date, time } = formatDateTime(votingEndTime);
                    return (
                      <div className="flex flex-row md:flex-col gap-1">
                        <p className="text-white">{date}</p>
                        <p className="text-white">{time}</p>
                      </div>
                    );
                  })()}
              </div>
            </div>
          </div>

          {children}
        </div>
      </div>
    </div>,
    document.body
  );
};
