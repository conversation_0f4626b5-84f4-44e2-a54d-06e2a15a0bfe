import { ReactNode } from "react";
import Image from "next/image";

interface Props {
  children: ReactNode;
  closeModal: () => void;
}
export const AirdropModal = ({ children, closeModal }: Props) => {
  const renderCloseButton = () => {
    return (
      <div className="flex flex-row justify-end items-center">
        <button onClick={closeModal} className="absolute right-4 top-4">
          <Image
            src="/assets/close_icon.svg"
            alt="close icon"
            width={15}
            height={15}
          />
        </button>
      </div>
    );
  };
  return (
    <div className="absolute top-0 left-0 w-full h-full bg-main-500/80 p-4 flex justify-center items-center">
      <div className="bg-main-1700 relative rounded-2xl md:h-auto p-4 py-12 md:px-8 md:py-12 md:w-[600px]">
        {renderCloseButton()}
        {children}
      </div>
    </div>
  );
};
