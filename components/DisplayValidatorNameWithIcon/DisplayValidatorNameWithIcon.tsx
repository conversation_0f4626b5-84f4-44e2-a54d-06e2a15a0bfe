"use client";

import { useMemo, useState, useRef } from "react";
import Image from "next/image";

interface Props {
  validatorName?: string;
  validatorAvatar?: string | null;
  maxNameLength?: number;
}

export const DisplayValidatorNameWithIcon = ({
  validatorName = "Unknown Validator",
  validatorAvatar,
  maxNameLength = 15,
}: Props) => {
  const defaultImg = "/assets/validator_icon.svg";
  const [isLoading, setIsLoading] = useState(true);
  const [imgError, setImgError] = useState(false);
  const errorCountRef = useRef(0);
  const maxErrorAttempts = 2;

  const imgSrc = useMemo(() => {
    if (imgError || !validatorAvatar) return defaultImg;
    return validatorAvatar;
  }, [validatorAvatar, imgError]);

  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    errorCountRef.current += 1;

    if (errorCountRef.current >= maxErrorAttempts) {
      setImgError(true);
    }

    e.currentTarget.src = defaultImg;
    setIsLoading(false);
  };

  // Ucinanie nazwy walidatora, jeśli jest dłuższa niż maxNameLength
  const displayName = useMemo(() => {
    if (!validatorName) return "Unknown Validator";

    if (validatorName.length > maxNameLength) {
      return `${validatorName.substring(0, maxNameLength)}...`;
    }

    return validatorName;
  }, [validatorName, maxNameLength]);

  return (
    <div className="flex items-center relative justify-center gap-1 flex-row">
      {isLoading && !imgError ? (
        <div className="absolute inset-0 bg-main-400 rounded-full flex items-center justify-center">
          <div className="w-5 h-5 border-2 border-main-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : null}
      <Image
        src={imgSrc}
        onError={handleImageError}
        onLoad={() => setIsLoading(false)}
        alt="validator image"
        width={20}
        height={20}
        className={`rounded-full ${
          isLoading && !imgError ? "opacity-0" : "opacity-100"
        }`}
      />
      <span
        className="text-sm text-main-600"
        title={validatorName.slice(0, 10) + "..."}
      >
        {displayName}
      </span>
    </div>
  );
};
