import { NodesBox } from "../NodesBox";

interface ActionButtonProps {
  text: string;
}

export const ActionButton = ({ text }: ActionButtonProps) => {
  return (
    <NodesBox>
      <div className="flex flex-col justify-center items-center h-full w-full">
        <button className="w-full text-center border border-white py-3 px-4 rounded-lg text-lg">
          {text}
        </button>
      </div>
    </NodesBox>
  );
};
