import { useState, useEffect } from "react";

export const NodesTable = () => {
  const [nodesData, setNodesData] = useState([]);

  useEffect(() => {
    setTimeout(() => {
      setNodesData([
        {
          address: "empel1tv2xw4lpd(...) ",
          type: "MicroNode",
          reward: "0.000136 EMPE",
          blockedUntil: "2025-02-17 11:00:00 AM",
          fullRewardFrom: "2025-02-17 11:00:00 AM",
        },
        {
          address: "empel1tv2xw4lpd(...) ",
          type: "Node",
          reward: "0.000136 EMPE",
          blockedUntil: "2025-02-17 11:00:00 AM",
          fullRewardFrom: "2025-02-17 11:00:00 AM",
        },
        {
          address: "empel1tv2xw4lpd(...) ",
          type: "Node",
          reward: "0.000136 EMPE",
          blockedUntil: "2025-02-17 11:00:00 AM",
          fullRewardFrom: "2025-02-17 11:00:00 AM",
        },
        {
          address: "empel1tv2xw4lpd(...) ",
          type: "Node",
          reward: "0.000136 EMPE",
          blockedUntil: "2025-02-17 11:00:00 AM",
          fullRewardFrom: "2025-02-17 11:00:00 AM",
        },
        {
          address: "empel1tv2xw4lpd(...) ",
          type: "Node",
          reward: "0.000136 EMPE",
          blockedUntil: "2025-02-17 11:00:00 AM",
          fullRewardFrom: "2025-02-17 11:00:00 AM",
        },
        {
          address: "empel1tv2xw4lpd(...) ",
          type: "Node",
          reward: "0.000136 EMPE",
          blockedUntil: "2025-02-17 11:00:00 AM",
          fullRewardFrom: "2025-02-17 11:00:00 AM",
        },
      ]);
    }, 1000);
  }, []);

  const thStyle = "border-r border-main-1400 text-center";

  return (
    <div className="bg-main-400 text-white rounded-lg p-4 overflow-x-auto">
      <table className="w-full text-left border-collapse validator-list min-w-[600px] md:min-w-0">
        <thead>
          <tr>
            <th
              className={`${thStyle} md:w-3/12 text-center text-main-800 font-normal`}
            >
              Address
            </th>
            <th
              className={`${thStyle} md:w-1/12 text-center text-main-800 font-normal`}
            >
              Node Type
            </th>
            <th
              className={`${thStyle} md:w-2/12 text-center text-main-800 font-normal`}
            >
              Reward
            </th>
            <th
              className={`${thStyle} md:w-2/12 text-center text-main-800 font-normal`}
            >
              Blocked Until
            </th>
            <th
              className={`${thStyle} md:w-2/12 text-center text-main-800 font-normal`}
            >
              Full Reward From
            </th>
            <th className="text-center md:w-3/12 text-main-800 font-normal">
              Action
            </th>
          </tr>
        </thead>
        <tbody>
          {nodesData.map((node, index) => (
            <tr key={index} className="border-b border-main-1400">
              <td className={`${thStyle} px-4 md:px-10`}>{node.address}</td>
              <td className={`${thStyle} px-2 md:px-4`}>{node.type}</td>
              <td className="p-4 md:p-6 border-r border-dark-700 text-blue-400 whitespace-nowrap">
                {node.reward}
              </td>
              <td className={thStyle}>{node.blockedUntil}</td>
              <td className={thStyle}>{node.fullRewardFrom}</td>
              <td className="flex items-center justify-center py-2 md:py-0">
                <button className="border border-white text-white px-4 md:px-8 py-1 md:py-2 rounded-lg mt-4 md:mt-1">
                  Details
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
