import { useState } from "react";
import Image from "next/image";

interface Props {
  src: string;
}

export const ImageWithOnError = ({ src }: Props) => {
  const [imgSrc, setImgSrc] = useState(src || "/assets/validator_icon.svg");

  return (
    <Image
      src={imgSrc}
      alt="validator image"
      width={30}
      height={30}
      onError={() => setImgSrc("/assets/validator_icon.svg")}
    />
  );
};
