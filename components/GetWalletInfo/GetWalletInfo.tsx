import { colorizeSubstring } from "@/utils/colorizeSubstring";
import Image from "next/image";
import Link from "next/link";

const TEXT = "Using the QR code below download &***&";
const SUBTEXT_TO_COLOR = "Empe Wallet";
export const GetWalletInfo = () => {
  return (
    <div className="flex flex-col gap-1 items-center justify-center">
      <div className="text-center mb-6 text-xl">
        {colorizeSubstring(TEXT, SUBTEXT_TO_COLOR, true)}
      </div>
      <Image
        src="/images/get-wallet.png"
        alt=""
        width={340}
        height={150}
        className={"hidden md:block"}
      />
      <div className="md:hidden overflow-hidden flex flex-row gap-4">
        <Link
          href="https://apps.apple.com/us/app/empe-wallet/id6468363115"
          target="_blank"
          className="md:hidden"
        >
          <Image src="/images/app_store.png" alt="" width={200} height={100} />
        </Link>
        <Link
          href="https://play.google.com/store/apps/details?id=io.empe.wallet"
          target="_blank"
          className="md:hidden"
        >
          <Image
            src="/images/google_play.png"
            alt=""
            width={200}
            height={100}
          />
        </Link>
      </div>
      <span className="mx-auto text-xl pt-2">then</span>
    </div>
  );
};
