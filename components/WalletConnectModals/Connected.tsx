import { WalletViewProps } from "@cosmos-kit/core";

import { getWalletProp, ModalViewImpl } from "@/config/walletConnect";
import Image from "next/image";
import {
  ConnectModalHead,
  ConnectModalStatus,
} from "@/components/WalletConnectModalsComponents";

export function ConnectedView({
  onClose,
  onReturn,
  wallet,
  options,
}: WalletViewProps): ModalViewImpl {
  const { walletInfo, username, address } = wallet;

  const onDisconnect = async () => {
    await wallet.disconnect(true, {
      walletconnect: {
        removeAllPairings: options?.mobile.displayQRCodeEveryTime,
      },
    });
  };

  const modalHead = (
    <ConnectModalHead
      title={walletInfo.prettyName}
      hasBackButton={true}
      onClose={onClose}
      onBack={onReturn}
    />
  );

  const modalContent = (
    <ConnectModalStatus
      wallet={getWalletProp(walletInfo)}
      status="Connected"
      connectedInfo={{
        name: username ?? "Wallet",
        avatar: (
          <Image
            src="/assets/astronaut.svg"
            alt="Astronaut"
            width={100}
            height={100}
            className="w-full h-full inherit"
          />
        ),
        address,
      }}
      onDisconnect={onDisconnect}
    />
  );

  return { head: modalHead, content: modalContent };
}
