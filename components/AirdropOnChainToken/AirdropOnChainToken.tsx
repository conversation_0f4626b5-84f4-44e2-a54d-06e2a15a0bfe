import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@empe/front-kit-next-ui";
import { useState } from "react";
import Image from "next/image";
import { useIsMobile } from "@/hooks";
import { colorizeSubstring } from "@/utils/colorizeSubstring";
import Link from "next/link";

interface Props {
  offeringQrUrl: string | undefined;
  offeringQrCode?: string | undefined;
}

const TEXT_STEP_1 =
  "Download the &***& mobile app (Android/iOS) to confirm your airdrop participation";
const TEXT_STEP_2 =
  "Use the &***& app to claim your Verifiable Credential - a digital proof of your participation";
const SUBTEXT_TO_COLOR = "Empe DID Wallet";

const DEEPLINK_URL =
  "empewallet://claim?offering_url=${encodeURIComponent(offering_full_url)}";

export const AirdropOnChainToken = ({
  offeringQrUrl,
  offeringQrCode,
}: Props) => {
  const isMobile = useIsMobile();
  const [step, setStep] = useState(0);

  const handleBackClick = () => {
    if (step === 0) return;
    setStep(step - 1);
  };

  const handleNextClick = () => {
    if (step === 3) return;
    setStep(step + 1);
  };

  const renderStep = () => {
    return <h1 className="text-2xl md:text-4xl text-center">Step {step}</h1>;
  };

  const renderNavigationButtons = () => {
    if (step === 0 || step === 3) return null;
    return (
      <div className="flex justify-between items-center">
        <div className="w-[120px]">
          <ButtonBasic onClick={handleBackClick}>Back</ButtonBasic>
        </div>
        <div className="w-[120px]">
          <ButtonGradient onClick={handleNextClick}>Next Step</ButtonGradient>
        </div>
      </div>
    );
  };

  const modalStep0 = () => {
    if (step !== 0) return null;
    return (
      <>
        <h1 className="text-2xl md:text-4xl font-bold text-center">
          How to claim <span className="text-main-100">Testnet Tokens</span>?
        </h1>
        <p className="text-base md:text-lg font-semibold text-center max-w-[500px]">
          Follow these steps in order to claim Testnet Tokens to your Keplr or
          Leap wallet for free!
        </p>
        <div className="w-full max-w-xs">
          <ButtonGradient onClick={handleNextClick}>Start</ButtonGradient>
        </div>

        <div className="flex flex-col gap-2 justify-center items-center">
          <p className="text-center text-sm md:text-base">
            or click the button below to go to the full guide documentation
          </p>
          <div className="w-full max-w-xs">
            <LinkBasic
              href="https://docs.empe.io/airdrop/faucet-guide-how-to-claim-testnet-tokens"
              target="_blank"
            >
              Guide Documentation
            </LinkBasic>
          </div>
        </div>
      </>
    );
  };

  const modalStep1 = () => {
    if (step !== 1) return null;
    return (
      <>
        {renderStep()}
        <div className="text-center text-md md:text-xl md:w-2/3 mx-auto">
          {colorizeSubstring(TEXT_STEP_1, SUBTEXT_TO_COLOR, false)}
        </div>
        <Image
          src="/images/get-wallet.png"
          alt=""
          width={400}
          height={400}
          className={"hidden md:block"}
        />
        <div className="md:hidden overflow-hidden flex flex-col gap-4">
          <Link
            href="https://apps.apple.com/us/app/empe-wallet/id6468363115"
            target="_blank"
            className="md:hidden"
          >
            <Image
              src="/images/wallet_appstore.png"
              alt=""
              width={200}
              height={100}
            />
          </Link>
          <Link
            href="https://play.google.com/store/apps/details?id=io.empe.wallet"
            target="_blank"
            className="md:hidden"
          >
            <Image
              src="/images/wallet_play.png"
              alt=""
              width={200}
              height={100}
            />
          </Link>
        </div>
      </>
    );
  };

  const modalStep2 = () => {
    if (step !== 2) return null;
    return (
      <>
        {renderStep()}
        <div className="text-center text-md md:text-xl md:w-2/3 mx-auto">
          {colorizeSubstring(TEXT_STEP_2, SUBTEXT_TO_COLOR, false)}
        </div>

        {offeringQrUrl && isMobile && (
          <LinkBorder
            href={DEEPLINK_URL.replace(
              "${encodeURIComponent(offering_full_url)}",
              encodeURIComponent(offeringQrUrl)
            )}
          >
            Claim your Verifiable Credential
          </LinkBorder>
        )}

        {offeringQrCode && !isMobile && (
          <div className="flex justify-center">
            <div className="bg-white p-2 rounded-lg">
              <Image
                src={offeringQrCode}
                alt="QR Code"
                width={250}
                height={250}
              />
            </div>
          </div>
        )}

        {(!offeringQrCode || !offeringQrUrl) && (
          <p className="text-center text-sm md:text-base p-4 rounded-xl border border-main-1300">
            The QR code or deep link couldn&apos;t be generated correctly.
            Please try again. If the issue persists, contact support.
          </p>
        )}
      </>
    );
  };

  const modalStep3 = () => {
    if (step !== 3) return null;
    return (
      <>
        {renderStep()}
        <p className="text-center text-lg md:text-2xl">
          {colorizeSubstring(
            "Verify your Verifiable Credential in the &***&",
            SUBTEXT_TO_COLOR,
            false
          )}
        </p>
        <p className="text-center text-xs md:text-base">
          {colorizeSubstring(
            "&***&",
            "*Once verified, testnet tokens will be automatically sent to your Keplr or Leap wallet",
            true
          )}
        </p>
        <p className="text-center text-base md:text-xl">
          {colorizeSubstring(
            "Click the button below to be redirected to the &***& and claim testnet tokens",
            "Faucet",
            false
          )}
        </p>
        <div className="text-center text-xs md:text-base flex flex-col">
          Use testnet tokens to complete on-chain actions and secure rewards -
          <a
            href={
              "https://docs.empe.io/airdrop/on-chain-testnet-airdrop#eligible-activities"
            }
            target="_blank"
            rel="noopener noreferrer"
            className="text-main-100 hover:text-main-200 underline"
          >
            learn more from the documentation
          </a>
        </div>
        <p className="text-center text-lg md:text-2xl">
          {colorizeSubstring("&***&", "Good Luck!", false)}
        </p>

        <div className="w-full max-w-xs">
          <LinkGradient href="https://faucet.empe.io" target="_blank">
            Verify your Verifiable Credential
          </LinkGradient>
        </div>
      </>
    );
  };

  const renderModalContent = () => {
    switch (step) {
      case 0:
        return modalStep0();
      case 1:
        return modalStep1();
      case 2:
        return modalStep2();
      default:
        return modalStep3();
    }
  };

  return (
    <div className="h-full w-full flex flex-col gap-10 md:gap-6 justify-between">
      <div className="flex flex-col gap-6 md:gap-10 justify-around items-center h-full w-full">
        {renderModalContent()}
      </div>
      {renderNavigationButtons()}
    </div>
  );
};
