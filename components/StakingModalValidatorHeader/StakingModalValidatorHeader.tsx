import { ValidatorDetailsJsonView } from "../ValidatorDetails";
import Image from "next/image";
import {
  formatValidatorMetaInfo,
  type ExtendedValidator as Validator,
} from "@/utils";
import Link from "next/link";

interface Props {
  selectedValidator: Validator;
  onClose: () => void;
  logoUrl: string;
}

export const StakingModalValidatorHeader = ({
  selectedValidator,
  onClose,
  logoUrl,
}: Props) => {
  return (
    <>
      <div className="flex flex-col md:flex-row justify-between gap-10 md:gap-2 items-center">
        <div className="flex flex-row items-center lg:gap-4 gap-4 md:gap-2 px-2">
          <Image
            src={logoUrl || "/assets/modal_image.png"}
            alt="modal image"
            width={64}
            height={64}
            className="lg:w-[64px] lg:h-[64px] w-[56px] h-[56px]"
          />
          <div className="flex flex-col gap-2 justify-center items-start ">
            <p className="text-white text-xl">{selectedValidator.name}</p>
            <p className="text-main-800 text-sm">
              {formatValidatorMetaInfo(selectedValidator)}
            </p>
          </div>
        </div>
        <Link
          className="text-main-600 border border-main-600 rounded-lg px-10 py-4"
          onClick={onClose}
          href={`/staking/validator-details/${selectedValidator.address}`}
        >
          Details
        </Link>
      </div>
      <ValidatorDetailsJsonView
        data={selectedValidator}
        addressKey="operator_address"
        excludeFields={["avatar_url", "reward", "description", "name", "apr"]}
      />
    </>
  );
};
