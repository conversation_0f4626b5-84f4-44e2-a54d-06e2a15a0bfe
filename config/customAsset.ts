import { AssetList } from "@chain-registry/types"; // Typy dla aktywów

export const customAssets: AssetList = {
  chain_name: "empe",
  assets: [
    {
      description: "Empe End-to-End Verifiable Data Infrastructure",
      extended_description:
        "Empeiria is the first End-to-End Verifiable Data Infrastructure (EVDI). It enables seamless web3 adoption through one-click deployment, empowering organizations with the data of the future",
      denom_units: [
        {
          denom: "uempe",
          exponent: 0,
        },
        {
          denom: "empe",
          exponent: 6,
        },
      ],
      base: "uempe",
      display: "empe",
      name: "EMP<PERSON>",
      symbol: "EMPE",
      logo_URIs: {
        png: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.png",
        svg: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.svg",
      },
      images: [
        {
          png: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.png",
          svg: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.svg",
        },
      ],
      type_asset: "sdk.coin",
    },
  ],
};


export const devnetAssets: AssetList = {
  chain_name: "empedevnet",
  assets: [
    {
      description: "Empe End-to-End Verifiable Data Infrastructure",
      extended_description:
          "Empeiria is the first End-to-End Verifiable Data Infrastructure (EVDI). It enables seamless web3 adoption through one-click deployment, empowering organizations with the data of the future",
      denom_units: [
        {
          denom: "uempe",
          exponent: 0,
        },
        {
          denom: "empe",
          exponent: 6,
        },
      ],
      base: "uempe",
      display: "empe",
      name: "EMPE",
      symbol: "EMPE",
      logo_URIs: {
        png: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.png",
        svg: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.svg",
      },
      images: [
        {
          png: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.png",
          svg: "https://raw.githubusercontent.com/cosmos/chain-registry/master/testnets/empetestnet/images/empe.svg",
        },
      ],
      type_asset: "sdk.coin",
    },
  ],
};
