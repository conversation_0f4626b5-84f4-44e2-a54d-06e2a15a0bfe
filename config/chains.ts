import { chains } from "chain-registry";
import { customChains } from "@/config/customChains";
import { ChainExt } from "@/types/chain";

chains.push(customChains[0]);
chains.push(customChains[1]);

const chainsWithAddress: ChainExt[] = chains.map((chain) => {
  if (chain.chain_name === "empedevnet") {
    console.log(`Adding addresses for chain: ${chain.chain_name}`);
    console.dir(chain, { depth: null });
    return {
      ...chain,
      addressHTTP: "https://hasura-devnet.empe.io/v1/graphql",
      addressWSS: "wss://hasura-devnet.empe.io/v1/graphql",
    };
  } else if (chain.chain_name === "empetestnet") {
    return {
      ...chain,
      addressHTTP: "https://hasura-testnet.empe.io/v1/graphql",
      addressWSS: "wss://hasura-testnet.empe.io/v1/graphql",
    };
  } else if (chain.chain_name === "empe") {
    return {
      ...chain,
      addressHTTP: "https://hasura.empe.io/v1/graphql",
      addressWSS: "wss://hasura.empe.io/v1/graphql",
    };
  }
  return chain;
});

const chainNames = ["empetestnet", "empe", "empedevnet"];

export const chainOptions = chainNames.map((chainName) => {
  const foundChain = chainsWithAddress.find(
    (chain) => chain.chain_name === chainName
  );
  console.dir(foundChain, { depth: null });
  if (!foundChain) {
    throw new Error(`Chain with name ${chainName} not found.`);
  }

  return foundChain;
});
