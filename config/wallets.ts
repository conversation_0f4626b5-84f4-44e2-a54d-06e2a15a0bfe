import { wallets as _wallets } from "cosmos-kit";
import { MainWalletBase } from "@cosmos-kit/core";
import { wallets as leap } from "@cosmos-kit/leap-metamask-cosmos-snap";
import { wallets as ledger } from "@cosmos-kit/ledger";
import { wallets as cosmos } from "@cosmos-kit/cosmos-extension-metamask";

export const wallets = [
  _wallets.keplr.extension,
  _wallets.leap.extension,
  _wallets.keplr.mobile,
  _wallets.leap.mobile,
  cosmos[0],
  ledger[0],
  leap[0],
] as MainWalletBase[];
