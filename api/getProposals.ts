export const handleGetProposals = async ({
  restApiUrl,
}: {
  restApiUrl: string;
}): Promise<any> => {
  const response = await fetch(`${restApiUrl}/cosmos/gov/v1/proposals`, {
    method: "GET",
    headers: {
      accept: "application/json",
    },
  });

  return response.json();
};

export const handleGetProposalsById = async ({
  restApiUrl,
  id,
}: {
  restApiUrl: string;
  id: number | string;
}): Promise<any> => {
  const response = await fetch(`${restApiUrl}/cosmos/gov/v1/proposals/${id}`, {
    method: "GET",
    headers: {
      accept: "application/json",
    },
  });

  return response.json();
};
