import {
  CreateOfferingRequestBody,
  OfferingResponse,
} from "@/types/issuerOffering";

const API_CONFIG = {
  url: process.env.NEXT_PUBLIC_ISSUER_CLIENT_URL,
};

export const handleGetQRCode = async (url: string) => {
  const response = await fetch(url);
  const data = await response.json();

  return data;
};

export const handleCreateOffering = async (
  opts: CreateOfferingRequestBody
): Promise<OfferingResponse> => {
  const response = await fetch(`${API_CONFIG.url}/offering/create`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(opts),
  });

  const data = await response.json();
  return data;
};
