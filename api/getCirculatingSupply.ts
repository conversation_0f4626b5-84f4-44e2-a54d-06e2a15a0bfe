export const handleGetTestnetCirculatingSupply = async (): Promise<any> => {
  const response = await fetch(
    "https://indexer-testnet.empe.io/circulating_supply",
    {
      method: "GET",
      headers: {
        accept: "application/json",
      },
    }
  );

  console.log("RES TEST", response);

  return response.json();
};

export const handleGetMainnetCirculatingSupply = async (): Promise<any> => {
  const response = await fetch("https://indexer.empe.io/circulating_supply", {
    method: "GET",
    headers: {
      accept: "application/json",
    },
  });

  console.log("RES", response);

  return response.json();
};
