import { Box } from "@/components/Box";
import { LayoutCenter } from "@/components/LayoutCenter";
import { withV<PERSON>datorCheck } from "@/hoc";
import { colorizeSubstring } from "@/utils/colorizeSubstring";
import { LinkGradient, LoaderSpinner } from "@empe/front-kit-next-ui";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useChainStore, useValidatorStore } from "@/store";
import { handleCreateOffering, handleGetQRCode } from "@/api/postOffering";
import { useChain } from "@cosmos-kit/react";
import { GetWalletInfo } from "@/components/GetWalletInfo";
import { useIsMobile } from "@/hooks";

const DESCRIPTION =
  "Scan the QR code to complete the validator verification and gain access to closed channels on\n&***&.";

const SUBSTRING_TO_COLOR = "Empe<PERSON><PERSON>'s Discord";

const DEEPLINK_URL =
  "empewallet://claim?offering_url=${encodeURIComponent(offering_full_url)}";

const ValidatorPage = () => {
  const { offeringQrUrl, setOfferingQrUrl } = useValidatorStore();
  const [offeringQrCode, setOfferingQrCode] = useState<string | null>(null);
  const { selectedChain } = useChainStore();
  const isMobile = useIsMobile();
  const { address, isWalletConnected } = useChain(selectedChain);

  useEffect(() => {
    const fetchQrCode = async () => {
      if (offeringQrUrl) return;
      try {
        const response = await handleCreateOffering({
          credential_subject: {
            did: address,
          },
          credential_type: "ProofOfEmpeValidator",
        });
        const qrCodeUrl = await handleGetQRCode(response.qr_code_url);
        setOfferingQrUrl(response.offering_full_url);
        setOfferingQrCode(qrCodeUrl);
      } catch (error) {
        console.error("Error fetching QR code:", error);
      }
    };

    if (isWalletConnected) {
      fetchQrCode();
    }
  }, [isWalletConnected, address]);

  const renderQrCode = () => {
    if (offeringQrUrl && isMobile) {
      return (
        <div className="flex justify-center items-center">
          <LinkGradient
            href={DEEPLINK_URL.replace(
              "${encodeURIComponent(offering_full_url)}",
              encodeURIComponent(offeringQrUrl)
            )}
          >
            Get your Validator VC
          </LinkGradient>
        </div>
      );
    }
    if (offeringQrCode) {
      return (
        <div className="flex justify-center items-center">
          <div className="p-4 border border-main-800 rounded-lg">
            <div className="flex justify-center">
              <div className="bg-white p-2 rounded-lg">
                <Image
                  src={offeringQrCode}
                  alt="QR Code"
                  width={200}
                  height={200}
                />
              </div>
            </div>
          </div>
        </div>
      );
    }
    return (
      <div className="flex justify-center items-center">
        <LoaderSpinner />
      </div>
    );
  };

  return (
    <LayoutCenter>
      <div className="h-full flex items-center justify-center w-full">
        <div className="w-fit h-fit max-w-xl">
          <Box>
            <div className="p-4 flex flex-col gap-4">
              <GetWalletInfo />
              <div className="text-center mb-6 text-xl">
                {colorizeSubstring(DESCRIPTION, SUBSTRING_TO_COLOR, true)}
              </div>
              {renderQrCode()}
            </div>
          </Box>
        </div>
      </div>
    </LayoutCenter>
  );
};

export default withValidatorCheck(ValidatorPage);
