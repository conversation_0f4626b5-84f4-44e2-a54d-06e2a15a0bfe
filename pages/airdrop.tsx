import { LayoutCenter } from "@/components/LayoutCenter";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useChainStore } from "@/store";
import { handleCreateOffering, handleGetQRCode } from "@/api/postOffering";
import { useAirdropStore } from "@/store/airdropStore";
import { useChain } from "@cosmos-kit/react";
import { airdropEvents } from "@/data/airdropEvents";
import { AirdropCard } from "@/components/AirdropCard";
import { AirdropHeader } from "@/components/AirdropHeader";
import { AirdropOnChainToken } from "@/components/AirdropOnChainToken";
import { AirdropModal } from "@/components/AirdropModal";

const AirdropPage = () => {
  const { offeringQrUrl, setOfferingQrUrl } = useAirdropStore();
  const [offeringQrCode, setOfferingQrCode] = useState<string | null>(null);
  const { selectedChain } = useChainStore();
  const [onChainModalOpen, setOnChainModalOpen] = useState(false);
  const { address, isWalletConnected } = useChain(selectedChain);

  const eventsCallback = [
    {
      event: "onChainEvent",
      callback: () => {
        setOnChainModalOpen(true);
      },
    },
    {
      event: "gEmpeEvent",
      callback: () => {
        window.open("https://t.me/gEMPE_bot", "_blank");
      },
    },
  ];

  useEffect(() => {
    const fetchQrCode = async () => {
      if (offeringQrUrl) return;
      try {
        const response = await handleCreateOffering({
          credential_subject: {
            did: address,
          },
          credential_type: "ProofOfAirdrop",
        });

        const qrCodeUrl = await handleGetQRCode(response.qr_code_url);
        setOfferingQrUrl(response.offering_full_url);
        setOfferingQrCode(qrCodeUrl);
      } catch (error) {
        console.error("Error fetching QR code:", error);
      }
    };

    if (isWalletConnected) {
      fetchQrCode();
    }
  }, [isWalletConnected, address]);

  const renderModalContent = () => {
    if (!isWalletConnected)
      return (
        <div className="flex flex-col justify-center gap-3 items-center">
          <Image
            src="/assets/empe_image.png"
            alt="empe image"
            width={75}
            height={75}
          />
          <div className="flex justify-center items-center rounded-lg border p-4 w-full">
            <div className="text-center text-xl">
              Connect your wallet to get your airdrop verifiable credential
            </div>
          </div>
        </div>
      );
    return (
      <AirdropOnChainToken
        offeringQrUrl={offeringQrUrl}
        offeringQrCode={offeringQrCode}
      />
    );
  };

  const renderModal = () => {
    if (!onChainModalOpen) return null;
    return (
      <AirdropModal closeModal={() => setOnChainModalOpen(false)}>
        {renderModalContent()}
      </AirdropModal>
    );
  };

  return (
    <LayoutCenter>
      <div className="h-full flex flex-col gap-10 items-center justify-center w-full">
        <AirdropHeader />
        {renderModal()}
        <div className="flex flex-wrap gap-6 justify-center items-center w-full">
          {airdropEvents.map((item, index) => {
            return (
              <AirdropCard
                {...item}
                key={`air-card-${index}`}
                onClick={eventsCallback[index].callback}
              />
            );
          })}
        </div>
      </div>
    </LayoutCenter>
  );
};

export default AirdropPage;
