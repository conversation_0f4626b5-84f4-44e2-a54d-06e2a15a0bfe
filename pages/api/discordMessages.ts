import { DiscordMessage } from "@/types/discordMessage";
import { DISCORD_CONFIG } from "@/config/discordConfig";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<DiscordMessage[] | { error: string }>
) {
  const channelId = DISCORD_CONFIG.NEXT_PUBLIC_DISCORD_CHANNEL_ID; // Zmień na ID swojego kanału
  const discordToken = DISCORD_CONFIG.DISCORD_BOT_TOKEN;

  if (!discordToken) {
    return res.status(500).json({ error: "Brak tokena w .env" });
  }

  try {
    const response = await fetch(
      `https://discord.com/api/v10/channels/${channelId}/messages?limit=10`,
      {
        method: "GET",
        headers: {
          Authorization: `Bot ${discordToken}`,
        },
      }
    );

    if (!response.ok) {
      return res
        .status(response.status)
        .json({ error: `Błąd API Discorda: ${response.statusText}` });
    }

    const messages: DiscordMessage[] = await response.json();

    res.status(200).json(
      messages.map((msg) => ({
        id: msg.id,
        channel_id: msg.channel_id,
        content: msg.content,
        attachments: msg.attachments.map((attachment) => ({
          url: attachment.url,
        })),
        author: { username: msg.author.username },
      }))
    );
  } catch (error) {
    console.error("Błąd pobierania wiadomości:", error);
    res.status(500).json({ error: "Wewnętrzny błąd serwera" });
  }
}
