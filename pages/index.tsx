import { Box } from "@/components/Box";
import { Announcements } from "@/widgets/Announcements";
import { Inflation } from "@/widgets/Inflation";
import { LatestBlocks } from "@/widgets/LatestBlocks";
import { LatestTransactions } from "@/widgets/LatestTransactions";
import { StakingAPR } from "@/widgets/StakingAPR";
import SummaryInfo from "@/widgets/SummaryInfo";
import { Supply } from "@/widgets/Supply";

export default function Dashboard() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col md:flex-row gap-4">
        <Box>
          <Supply />
        </Box>
        <div className="flex flex-col gap-4">
          <Box>
            <StakingAPR />
          </Box>
          <Box>
            <Inflation />
          </Box>
        </div>
      </div>
      <Box>
        <SummaryInfo />
      </Box>
      <div className="flex flex-col md:flex-row gap-4">
        <Box>
          <LatestBlocks />
        </Box>

        <Box>
          <LatestTransactions />
        </Box>

        <Box>
          <Announcements />
        </Box>
      </div>
    </div>
  );
}
