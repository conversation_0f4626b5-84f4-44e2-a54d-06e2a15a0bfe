"use client";

import { useStakingModals } from "@/contexts/StakingModalsContext";
import {
  TableBasic,
  RadioButtonBasic,
  LoaderSpinner,
} from "@empe/front-kit-next-ui";
import { stakingColumnsSelectValidator } from "@/components/staking/config";
import { ValidatorWithImage } from "@/components/staking/ValidatorWIthImage";
import { useDashboardStore } from "@/store/dashboardStore";
import { calculateVotingPower } from "@/utils/calculateVotingPower";
import { type ExtendedValidator as Validator } from "@/utils";
import { useState, useEffect } from "react";
import { useUptime } from "@/hooks/staking/useUptime";
import { MobileTable } from "@/components/MobileTable/MobileTable";

const SelectValidatorPage = () => {
  const { bondedTokens } = useDashboardStore();
  const { uptime } = useUptime();
  const [selectedRecipientValidator, setSelectedRecipientValidator] =
    useState<Validator | null>(null);
  const {
    renderModals,
    openRedelegateModal,
    selectedValidator,
    stakingConfig,
  } = useStakingModals();

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (stakingConfig?.allValidators) {
      setIsLoading(false);
    }
  }, [stakingConfig?.allValidators]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoaderSpinner />
      </div>
    );
  }

  const handleOpenRedelegateModal = () => {
    if (selectedRecipientValidator) {
      openRedelegateModal(selectedValidator, selectedRecipientValidator);
    }
  };

  const data = (stakingConfig?.allValidators ?? []).map((validator) => ({
    radio: {
      value: (
        <RadioButtonBasic
          id={validator.address}
          name={validator.address}
          checked={validator.address === selectedRecipientValidator?.address}
          onChange={() => setSelectedRecipientValidator(validator)}
        />
      ),
      sortKey: validator.address,
    },
    validator: {
      value: (
        <ValidatorWithImage
          validator={validator}
          logos={stakingConfig?.logos}
        />
      ),
      sortKey: validator.name,
    },
    commission: validator.commission,
    uptime: uptime[validator.address]?.uptime || 0,
    reward: validator.reward,
    votingPower: calculateVotingPower(validator.votingPower, bondedTokens),
  }));

  const mobileData = (stakingConfig?.allValidators ?? []).map((validator) => ({
    validator: {
      value: (
        <div className="flex flex-row gap-4">
          <RadioButtonBasic
            id={validator.address}
            name={validator.address}
            checked={validator.address === selectedRecipientValidator?.address}
            onChange={() => setSelectedRecipientValidator(validator)}
          />
          <ValidatorWithImage
            validator={validator}
            logos={stakingConfig?.logos}
            staticRow={true}
          />
        </div>
      ),
      sortKey: validator.name,
    },
    commission: validator.commission,
    uptime: uptime[validator.address]?.uptime || 0,
    reward: validator.reward,
    votingPower: calculateVotingPower(validator.votingPower, bondedTokens),
  }));

  const renderTable = () => {
    return (
      <>
        <div className="absolute hidden md:block rounded-lg inset-x-0 top-12 bottom-16 overflow-auto without-scrollbar">
          <TableBasic data={data} columns={stakingColumnsSelectValidator} />
        </div>
        <div className="absolute md:hidden rounded-lg inset-x-0 top-12 bottom-16 overflow-auto without-scrollbar">
          <MobileTable
            data={mobileData}
            columns={stakingColumnsSelectValidator.slice(1)}
          />
        </div>
      </>
    );
  };

  return (
    <div className="flex max-w-screen-xl mx-auto flex-col h-full w-full relative">
      <div className="absolute inset-x-0 top-0 h-12 flex items-center">
        <h2 className="text-2xl">Choose Validator:</h2>
      </div>
      {renderTable()}
      <div className="absolute inset-x-0 bottom-0 w-full h-16 flex items-center">
        <button
          className={`py-2 px-4 lg:w-1/2 mx-auto text-white font-semibold rounded-lg shadow-md w-full transition-all max-w-[50%] ${
            selectedRecipientValidator
              ? "bg-gradient-to-r from-[#00AFFF] via-[#006EFF] to-[#E200ED] hover:from-[#0099E0] hover:via-[#005BCC] hover:to-[#C700D6] max-w-[50%] mx-auto"
              : "bg-[#373737] cursor-not-allowed"
          }`}
          disabled={!selectedRecipientValidator}
          onClick={handleOpenRedelegateModal}
        >
          Redelegate
        </button>
      </div>
      {renderModals()}
    </div>
  );
};

export default SelectValidatorPage;
