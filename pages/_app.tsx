import "../styles/globals.css";
import "../styles/gradients.css";
import "@interchain-ui/react/styles";

import "@empe/front-kit-next-ui/index.css";

import type { AppProps } from "next/app";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { Toaster } from "@interchain-ui/react";
import { chains, assets } from "chain-registry";

import { CustomThemeProvider, Layout } from "@/components";

import { wallets } from "@/config";
import { getSignerOptions } from "@/utils";
import { SeoHead } from "@/components/SeoHead";
import {
  ConnectedView,
  ConnectingView,
  ErrorView,
  NotExistView,
  QRCodeView,
  RejectedView,
  WalletListView,
} from "@/components/WalletConnectModals";
import { ChainProvider } from "@/providers/mainChainProvider";
import React from "react";
import { MainDataProvider } from "@/contexts/MainDataContext";
import { useRouter } from "next/router";
import { StakingModalsProvider } from "@/contexts/StakingModalsContext";
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    },
  },
});

// Access the Google Analytics ID from the environment variable
const googleAnalyticsConfig = {
  id: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
  tag: process.env.NEXT_PUBLIC_GOOGLE_TAG_ID,
};

function CreateCosmosApp({ Component, pageProps }: AppProps) {
  const pageComponent = React.createElement(Component, pageProps);
  const router = useRouter();

  const renderContentWithSpecificProviders = () => {
    if (router.pathname.startsWith("/staking")) {
      return <StakingModalsProvider>{pageComponent}</StakingModalsProvider>;
    }

    return pageComponent;
  };

  return (
    <CustomThemeProvider>
      <SeoHead />
      {/* Google Analytics */}
      <GoogleAnalytics gaId={googleAnalyticsConfig.id} />
      <GoogleTagManager gtmId={googleAnalyticsConfig.tag} />
      <ChainProvider
        chains={chains}
        assetLists={assets}
        wallets={wallets}
        walletConnectOptions={{
          signClient: {
            projectId: "f88db53308a054e93d19fbe68e622ea5",
            relayUrl: "wss://relay.walletconnect.org",
            metadata: {
              name: "Empeiria",
              description: "AppKit Example",
              url: "https://reown.com/appkit", // origin must match your domain & subdomain
              icons: ["https://assets.reown.com/reown-profile-pic.png"],
            },
          },
        }}
        modalTheme={{
          modalContainerClassName: "bg-main-1700 rounded-lg",
          modalContentClassName: "bg-transparent",
          modalContentStyles: {
            backgroundColor: "transparent",
          },
        }}
        modalViews={{
          WalletList: WalletListView,
          QRCode: QRCodeView,
          Connecting: ConnectingView,
          Connected: ConnectedView,
          Rejected: RejectedView,
          Error: ErrorView,
          NotExist: NotExistView,
        }}
        signerOptions={getSignerOptions()}
      >
        <QueryClientProvider client={queryClient}>
          <Layout>
            <MainDataProvider>
              {renderContentWithSpecificProviders()}
            </MainDataProvider>
            <Toaster position="top-right" closeButton={true} />
          </Layout>
        </QueryClientProvider>
      </ChainProvider>
    </CustomThemeProvider>
  );
}

export default CreateCosmosApp;
