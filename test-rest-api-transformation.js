// Test script do sprawdzenia transformacji danych z REST API
// Uruchom: node test-rest-api-transformation.js

const testRestProposal = {
  "id": "1",
  "messages": [
    {
      "@type": "/cosmos.gov.v1.MsgExecLegacyContent",
      "content": {
        "@type": "/cosmos.params.v1beta1.ParameterChangeProposal",
        "title": "Staking - Increase Max Validators",
        "description": "Proposal to increase the maximum number of active validators from 100 to 200."
      }
    }
  ],
  "status": "PROPOSAL_STATUS_PASSED",
  "final_tally_result": {
    "yes_count": "27859824088",
    "abstain_count": "0",
    "no_count": "0",
    "no_with_veto_count": "0"
  },
  "submit_time": "2024-07-29T19:02:52.251883059Z",
  "deposit_end_time": "2024-07-31T19:02:52.251883059Z",
  "total_deposit": [
    {
      "denom": "uempe",
      "amount": "50000000"
    }
  ],
  "voting_start_time": "2024-07-29T19:02:52.251883059Z",
  "voting_end_time": "2024-07-29T20:02:52.251883059Z",
  "metadata": "",
  "title": "Staking - Increase Max Validators",
  "summary": "Proposal to increase the maximum number of active validators from 100 to 200.",
  "proposer": "empe1lzhz50370v2kngwgx4r6f2e426kykpdt98xy9p"
};

function transformRestProposalToRpcFormat(restProposal) {
  return {
    ...restProposal,
    // Konwersja dat ze stringów na obiekty Date
    submitTime: restProposal.submit_time ? new Date(restProposal.submit_time) : undefined,
    depositEndTime: restProposal.deposit_end_time ? new Date(restProposal.deposit_end_time) : undefined,
    votingStartTime: restProposal.voting_start_time ? new Date(restProposal.voting_start_time) : undefined,
    votingEndTime: restProposal.voting_end_time ? new Date(restProposal.voting_end_time) : undefined,
    
    // Mapowanie pól z snake_case na camelCase
    finalTallyResult: restProposal.final_tally_result ? {
      yesCount: restProposal.final_tally_result.yes_count,
      noCount: restProposal.final_tally_result.no_count,
      abstainCount: restProposal.final_tally_result.abstain_count,
      noWithVetoCount: restProposal.final_tally_result.no_with_veto_count,
    } : undefined,
    
    totalDeposit: restProposal.total_deposit,
    
    // Zachowanie oryginalnych pól dla kompatybilności
    submit_time: restProposal.submit_time,
    deposit_end_time: restProposal.deposit_end_time,
    voting_start_time: restProposal.voting_start_time,
    voting_end_time: restProposal.voting_end_time,
    final_tally_result: restProposal.final_tally_result,
    total_deposit: restProposal.total_deposit,
  };
}

console.log('=== TEST TRANSFORMACJI DANYCH Z REST API ===\n');

console.log('Oryginalne dane z REST API:');
console.log('- submit_time:', testRestProposal.submit_time);
console.log('- voting_end_time:', testRestProposal.voting_end_time);
console.log('- final_tally_result:', testRestProposal.final_tally_result);
console.log('- status:', testRestProposal.status);
console.log('- title:', testRestProposal.title);

const transformed = transformRestProposalToRpcFormat(testRestProposal);

console.log('\nPo transformacji:');
console.log('- submitTime:', transformed.submitTime);
console.log('- votingEndTime:', transformed.votingEndTime);
console.log('- finalTallyResult:', transformed.finalTallyResult);
console.log('- status:', transformed.status);
console.log('- title:', transformed.title);

console.log('\nSprawdzenie czy daty są obiektami Date:');
console.log('- submitTime instanceof Date:', transformed.submitTime instanceof Date);
console.log('- votingEndTime instanceof Date:', transformed.votingEndTime instanceof Date);

console.log('\nSprawdzenie filtrowania po dacie (czy proposal jest aktywny):');
const currentDate = new Date();
const isActive = new Date(transformed.votingEndTime) > currentDate;
console.log('- Current date:', currentDate.toISOString());
console.log('- Voting end time:', transformed.votingEndTime.toISOString());
console.log('- Is active (voting_end_time > current_date):', isActive);

console.log('\n=== TEST ZAKOŃCZONY ===');
