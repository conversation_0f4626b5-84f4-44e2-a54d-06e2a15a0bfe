# <PERSON><PERSON><PERSON><PERSON> obrazu node:18-alpine jako środowiska wykonawczego
FROM node:18-alpine AS runner

# Ustawiamy katalog roboczy na /app
WORKDIR /app

# Ustawiamy zmienną środowiskową NODE_ENV na production
ENV NODE_ENV production

# Kopiujemy katalog standalone zawierający aplikację
COPY dist/standalone ./

# Kopiujemy katalog static z zasobami
COPY dist/static ./dist/static

# Kopiujemy katalog public z zasobami
COPY public ./public

# Ustawiamy odpowiednie uprawnienia dla aplikacji
RUN chmod -R 755 .

# Otwieramy port 3000, na którym działa aplikacja
EXPOSE 3000

# Ustawiamy zmienną środowiskową HOSTNAME na 0.0.0.0, aby aplikacja nasłuchiwała na wszystkich interfejsach
ENV HOSTNAME "0.0.0.0"

# Uruchamiamy aplikację
CMD ["node", "server.js"]
