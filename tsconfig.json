{"compilerOptions": {"strictNullChecks": false, "target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["dist", "node_modules", ".storybook", ".storybook/**", "**/.storybook", "**/.storybook/**", "**/*.stories.tsx", "**/*.stories.ts", "**/*.storybook.tsx", "**/*.storybook.ts"]}