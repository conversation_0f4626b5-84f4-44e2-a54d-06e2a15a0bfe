import React from 'react';
import { useVotingData } from '@/hooks/voting/useVotingData';

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> użycia nowej implementacji proposals z REST API
 */
export const ProposalsExample: React.FC = () => {
  // Przykład dla różnych chainów
  const empeData = useVotingData('empe');
  const testnetData = useVotingData('empetestnet');
  const devnetData = useVotingData('empedevnet');

  return (
    <div>
      <h2>Proposals z REST API</h2>
      
      {/* Empe Mainnet */}
      <div>
        <h3>Empe Mainnet</h3>
        {empeData.isLoading ? (
          <p>Ładowanie proposals...</p>
        ) : (
          <div>
            <p>Znaleziono {empeData.data?.proposals?.length || 0} proposals</p>
            {empeData.data?.proposals?.map((proposal) => (
              <div key={proposal.id.toString()}>
                <h4>Proposal #{proposal.id.toString()}</h4>
                <p>Status: {proposal.status}</p>
                <p>Tytuł: {proposal.content?.title || 'Brak tytułu'}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Empe Testnet */}
      <div>
        <h3>Empe Testnet</h3>
        {testnetData.isLoading ? (
          <p>Ładowanie proposals...</p>
        ) : (
          <div>
            <p>Znaleziono {testnetData.data?.proposals?.length || 0} proposals</p>
            {testnetData.data?.proposals?.map((proposal) => (
              <div key={proposal.id.toString()}>
                <h4>Proposal #{proposal.id.toString()}</h4>
                <p>Status: {proposal.status}</p>
                <p>Tytuł: {proposal.content?.title || 'Brak tytułu'}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Empe Devnet */}
      <div>
        <h3>Empe Devnet</h3>
        {devnetData.isLoading ? (
          <p>Ładowanie proposals...</p>
        ) : (
          <div>
            <p>Znaleziono {devnetData.data?.proposals?.length || 0} proposals</p>
            {devnetData.data?.proposals?.map((proposal) => (
              <div key={proposal.id.toString()}>
                <h4>Proposal #{proposal.id.toString()}</h4>
                <p>Status: {proposal.status}</p>
                <p>Tytuł: {proposal.content?.title || 'Brak tytułu'}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Przyciski do odświeżania */}
      <div style={{ marginTop: '20px' }}>
        <button onClick={() => empeData.refetch()}>
          Odśwież Empe Mainnet
        </button>
        <button onClick={() => testnetData.refetch()}>
          Odśwież Empe Testnet
        </button>
        <button onClick={() => devnetData.refetch()}>
          Odśwież Empe Devnet
        </button>
      </div>
    </div>
  );
};

/**
 * Przykład testowania REST endpoints bezpośrednio
 */
export const TestRestEndpoints: React.FC = () => {
  const [results, setResults] = React.useState<Record<string, any>>({});

  const testEndpoint = async (chainName: string) => {
    try {
      const { getRestApiUrl } = await import('@/utils/chain');
      const { handleGetProposals } = await import('@/api/getProposals');
      
      const restApiUrl = getRestApiUrl(chainName);
      if (!restApiUrl) {
        throw new Error(`REST API URL not found for ${chainName}`);
      }

      console.log(`Testing ${chainName}: ${restApiUrl}`);
      const response = await handleGetProposals({ restApiUrl });
      
      setResults(prev => ({
        ...prev,
        [chainName]: {
          success: true,
          url: restApiUrl,
          proposalsCount: response.proposals?.length || 0,
          data: response
        }
      }));
    } catch (error) {
      console.error(`Error testing ${chainName}:`, error);
      setResults(prev => ({
        ...prev,
        [chainName]: {
          success: false,
          error: error.message,
          url: null
        }
      }));
    }
  };

  return (
    <div>
      <h2>Test REST Endpoints</h2>
      
      <div>
        <button onClick={() => testEndpoint('empe')}>Test Empe Mainnet</button>
        <button onClick={() => testEndpoint('empetestnet')}>Test Empe Testnet</button>
        <button onClick={() => testEndpoint('empedevnet')}>Test Empe Devnet</button>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Wyniki testów:</h3>
        <pre>{JSON.stringify(results, null, 2)}</pre>
      </div>
    </div>
  );
};
