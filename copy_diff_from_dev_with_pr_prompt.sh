#!/bin/bash

# Sprawd<PERSON>ie, czy branch main istnieje
if ! git rev-parse --verify main &> /dev/null; then
    echo "Branch 'main' nie istnieje. Upewnij się, że masz go lokalnie."
    exit 1
fi

# Lista plików do ignorowania (typowe dla projektów React/React Native/Next.js/Vite/Storybook)
IGNORE_FILES=(
    "yarn.lock"
    "package-lock.json"
    "package.json"
    "pnpm-lock.yaml"
    "node_modules/*"
    "**/*.png"
    "**/*.jpg"
    "**/*.jpeg"
    "**/*.gif"
    "**/*.svg"
    "**/*.webp"
    "**/*.ico"
    ".env"
    ".env.*"
    ".gitignore"
    "tsconfig.json"
    "tsconfig.*.json"
    "babel.config.js"
    "metro.config.js"
    "next.config.js"
    "vite.config.js"
    "jest.config.js"
    "storybook/**/*.json"
    "storybook/**/*.js"
    ".eslint*"
    ".prettier*"
    ".nvmrc"
    ".editorconfig"
    "webpack.config.js"
    "babel.config.cjs"
)

# Pobranie listy plików różniących się względem main
FILES=$(git diff --name-only main)

# Jeśli brak zmian, zakończ skrypt
if [[ -z "$FILES" ]]; then
    echo "Brak plików różniących się względem main."
    exit 0
fi

# Filtrowanie plików – usuwamy te, które są na liście ignorowanych
FILTERED_FILES=()
for file in $FILES; do
    SKIP=false
    for pattern in "${IGNORE_FILES[@]}"; do
        if [[ $file == $pattern ]]; then
            SKIP=true
            break
        fi
    done
    if [[ $SKIP == false ]]; then
        FILTERED_FILES+=("$file")
    fi
done

# Jeśli po przefiltrowaniu nie ma żadnych plików, zakończ skrypt
if [[ ${#FILTERED_FILES[@]} -eq 0 ]]; then
    echo "Brak istotnych plików do skopiowania względem main."
    exit 0
fi

# Prompt do code review
PROMPT="
Jesteś doświadczonym mentorem ds. code review specjalizującym się w React. Twoim zadaniem jest przeanalizowanie poniższego kodu i udzielenie szczegółowego, konstruktywnego feedbacku. Podczas analizy skup się na następujących aspektach:

1. Szczegółowa analiza problematycznych fragmentów:
    - Identyfikacja problemów: Skup się wyłącznie na liniach lub fragmentach kodu, w których występują problemy lub można wprowadzić ulepszenia.
2. Diagnoza i poprawki:
    - Dla każdego wykrytego problemu lub nieoptymalnego fragmentu:
        - Podaj krótki fragment kodu ilustrujący dany problem.
        - Wyjaśnij, na czym polega problem (np. błąd, ryzyko bezpieczeństwa, nieczytelne nazewnictwo, naruszenie zasad clean code, niejasność intencji).
        - Zaproponuj konkretną poprawkę z uzasadnieniem.
        - Oceń wpływ problemu na działanie, bezpieczeństwo lub utrzymanie kodu. Unikaj nadmiernego „nitpickingu” w przypadku drobnych kwestii.
    - Usuwanie nieużywanych elementów: Upewnij się, że wszystkie importy, hooki i inne elementy, które nie są używane w kodzie, zostały usunięte. Zidentyfikowanie i usunięcie niepotrzebnych zależności zmniejsza wielkość bundla i poprawia czytelność kodu.
3. Komentarze i intencje:
    - Wartość komentarzy: Sprawdź, czy istniejące komentarze dodają wartość – powinny wyjaśniać dlaczego dany fragment został zaimplementowany w określony sposób, a nie tylko co robi.
    - Zasugeruj dodanie komentarzy w miejscach, gdzie mogą one zwiększyć klarowność intencji.
    - Wskaż miejsca, gdzie komentarz jest zbędny (opisuje oczywiste operacje) lub brakuje wyjaśnienia skomplikowanych instrukcji.
4. Nazewnictwo i struktura kodu:
    - Czytelność i zgodność z konwencjami: Oceń, czy nazwy funkcji, zmiennych i parametrów są klarowne i zgodne z konwencjami (np. camelCase dla zmiennych i funkcji, PascalCase dla komponentów).
    - Podział odpowiedzialności: Sprawdź, czy funkcje realizują pojedynczą odpowiedzialność (Single Responsibility Principle). Jeśli funkcja lub komponent wykonuje zbyt wiele zadań, zaproponuj podział na mniejsze, bardziej logiczne fragmenty.
    - Klarowność nazw: Zidentyfikuj niejednoznaczne lub mylące nazewnictwo. Zaproponuj bardziej opisowe i jednoznaczne alternatywy.
5. Inicjalizacja i zarządzanie stanem (React):
    - Poprawność inicjalizacji: Zwróć uwagę na inicjalizację stanów i zmiennych. Wskaż ryzyko użycia niezainicjowanych wartości.
    - Synchronizacja i zależności: Oceń, czy zależności w hookach są prawidłowo określone. Zwróć uwagę na potencjalne problemy z niespójnym stanem lub niepoprawnym renderowaniem (np. brak kluczowych zależności w useEffect).
6. Optymalizacja i wydajność:
    - Wydajność: Zidentyfikuj potencjalne wąskie gardła wydajnościowe, takie jak zbędne re-rendery, kosztowne obliczenia w cyklu renderowania, lub brak memoizacji.
    - Logika biznesowa i komponenty prezentacyjne: Sprawdź, czy logika biznesowa jest oddzielona od komponentów prezentacyjnych. Zaoferuj wyodrębnienie logiki do niestandardowych hooków lub komponentów kontenerowych, jeśli to konieczne.
    - Obsługa błędów i stanów ładowania: Oceń, czy komponent poprawnie obsługuje stany ładowania, błędy i inne stany brzegowe. Zaproponuj dodanie odpowiednich mechanizmów obsługi błędów i stanów ładowania.
7. Zgodność z dobrymi praktykami React:
    - Dobre praktyki: Sprawdź, czy kod jest zgodny z zalecanymi praktykami React, takimi jak unikanie bezpośredniej manipulacji DOM, używanie kluczy (key) w listach, i inne.
    - Komentarze i dokumentacja: Sprawdź, czy kod posiada komentarze, a jeśli nie, to aby były dodane, aby każdy developer, który będzie miał styczność z kodem, mógł zrozumieć jego działanie.
8. Sugestie poprawek i ulepszona wersja kodu:
    - Podsumowanie problemów: Wymień wszystkie wykryte problemy z krótkim opisem i wskazówkami dotyczącymi poprawy.
    - Poprawiona wersja kodu: Zaprezentuj kompletną, poprawioną wersję kodu zgodną z dobrymi praktykami Reacta. W poprawionej wersji stosuj czytelne nazewnictwo, poprawną strukturę i prawidłowe zarządzanie stanem.

Dodatkowo: Upewnij się, że kod nie zawiera nieużywanych hooków, importów i innych elementów, które mogą obciążać projekt lub utrudniać jego dalszy rozwój. Usuń wszelkie zbędne zależności, aby poprawić czytelność i wydajność kodu.
"

# Przygotowanie zawartości do skopiowania
OUTPUT="$PROMPT\n\nLista plików zmienionych względem 'main':\n\n"

for file in "${FILTERED_FILES[@]}"; do
    if [[ -f "$file" ]]; then  # Sprawdzenie, czy to plik, a nie katalog
        OUTPUT+="--- $file ---\n"
        OUTPUT+="$(cat "$file")\n\n"
    else
        OUTPUT+="--- $file --- (Plik usunięty)\n\n"
    fi
done

# Kopiowanie do schowka w zależności od systemu
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "$OUTPUT" | pbcopy  # macOS
    echo "Lista zmienionych plików + prompt do code review skopiowana do schowka!"
elif command -v xclip &> /dev/null; then
    echo -e "$OUTPUT" | xclip -selection clipboard  # Linux z xclip
    echo "Lista zmienionych plików + prompt do code review skopiowana do schowka!"
elif command -v xsel &> /dev/null; then
    echo -e "$OUTPUT" | xsel --clipboard  # Linux z xsel
    echo "Lista zmienionych plików + prompt do code review skopiowana do schowka!"
else
    echo "Nie znaleziono narzędzia do kopiowania do schowka. Skopiuj ręcznie:"
    echo -e "$OUTPUT"
fi
