import { useDashboardStore } from "@/store/dashboardStore";
import Image from "next/image";

export const Inflation = () => {
  const { inflation } = useDashboardStore();

  if (!inflation) return null;

  const inflationPercentage = (inflation * 100).toFixed(2);

  return (
    <div className="flex h-full items-center px-4 text-white w-full relative whitespace-nowrap gap-2">
      <div className="border-r border-white/10 pr-4 h-full flex items-center">
        <Image
          src="/assets/ChartUp.svg"
          alt="Inflation"
          width={32}
          height={32}
        />
      </div>
      <div className="flex flex-col items-start text-left pl-4">
        <div className="text-sm">Inflation:</div>
        <div className="text-2xl">{inflationPercentage}%</div>
      </div>
    </div>
  );
};
