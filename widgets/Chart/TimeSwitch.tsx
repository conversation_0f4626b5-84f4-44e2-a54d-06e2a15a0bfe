// components/TimeSwitch.tsx
import React from "react";

const TimeSwitch = () => {
  return (
    <div className="flex space-x-2 bg-gray-800 p-2 rounded-lg">
      {["24h", "7d", "30d", "All"].map((time) => (
        <button
          key={time}
          className="px-3 py-1 bg-gray-700 text-white rounded hover:bg-gray-600"
        >
          {time}
        </button>
      ))}
    </div>
  );
};

export default TimeSwitch;
