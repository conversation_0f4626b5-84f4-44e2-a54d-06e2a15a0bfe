// components/Chart.tsx
import React from "react";

export interface ChartDataPoint {
  x: number;
  y: number;
}

export const mockChartData: ChartDataPoint[] = [
  { x: 0, y: 30 },
  { x: 20, y: 10 },
  { x: 40, y: 30 },
  { x: 60, y: 20 },
  { x: 80, y: 30 },
  { x: 100, y: 25 },
];

const Chart = () => {
  const pathData = mockChartData
    .map((point, index, array) => {
      if (index === 0) {
        return `M${point.x},${point.y}`;
      }
      const prevPoint = array[index - 1];
      const midX = (prevPoint.x + point.x) / 2;
      return `Q${midX},${prevPoint.y} ${point.x},${point.y}`;
    })
    .join(" ");

  return (
    <svg viewBox="0 0 100 50" className="w-full h-32">
      <defs>
        <linearGradient id="gradient" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stopColor="#00f" stopOpacity="0.4" />
          <stop offset="100%" stopColor="#00f" stopOpacity="0" />
        </linearGradient>
      </defs>
      <path d={`${pathData} V50 H0 Z`} fill="url(#gradient)" stroke="none" />
      <path d={pathData} fill="none" stroke="#00f" strokeWidth="1" />
    </svg>
  );
};

export default Chart;
