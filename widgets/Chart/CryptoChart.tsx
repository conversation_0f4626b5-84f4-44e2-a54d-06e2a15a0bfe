import React, { useState } from "react";

export const CryptoChart = () => {
  const [data, setData] = useState([60, 40, 80, 50, 20, 80, 60, 40, 80, 50]);

  const generateSmoothPath = (data: number[]) => {
    const width = 500;
    const height = 100;
    const step = width / (data.length - 1);
    let d = `M 0 ${height - data[0]}`;

    for (let i = 1; i < data.length; i++) {
      const x1 = (i - 1) * step + step / 3;
      const y1 = height - data[i - 1];
      const x2 = i * step - step / 3;
      const y2 = height - data[i];
      const x = i * step;
      const y = height - data[i];
      d += ` C ${x1} ${y1}, ${x2} ${y2}, ${x} ${y}`;
    }

    return d;
  };

  return (
    <div className=" text-white w-full h-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">EMPE PRICE</h2>
        <div className="flex space-x-2 bg-gray-800 p-1 rounded-lg">
          {["24h", "7d", "30d", "All"].map((label, index) => (
            <button
              key={index}
              className={`px-3 py-1 text-sm rounded-md ${
                label === "24h" ? "bg-white text-black" : "text-gray-400"
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>
      <div className="text-4xl font-bold">$17</div>
      <div className="text-green-400 text-sm">+234.56% (24h)</div>
      <div className="my-4 relative">
        <svg viewBox="0 0 500 100" className="w-full h-full">
          <defs>
            <linearGradient id="shadowGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#00bfff" stopOpacity="0.5" />
              <stop offset="100%" stopColor="#00bfff" stopOpacity="0" />
            </linearGradient>
          </defs>
          <path
            d={generateSmoothPath(data)}
            stroke="#00bfff"
            strokeWidth="1"
            fill="none"
          />
          <path
            d={`${generateSmoothPath(data)} L 500 100 L 0 100 Z`}
            fill="url(#shadowGradient)"
            opacity="0.5"
          />
        </svg>
      </div>
      <div className="text-sm text-gray-400">Market cap:</div>
      <div className="text-2xl font-semibold">$2,385,317,632</div>
    </div>
  );
};
