import { DashboardSummaryInfoTypes } from "@/enums/dashboardSummaryInfoTypes";
import { useSummaryCount } from "@/hooks/dashboard/useSummaryCount";
import { Fragment } from "react";
import { SummaryItem } from "./SummaryItem";

const summaryInfo = [
  {
    icon: "/assets/Did.svg",
    title: "DID",
    valueKey: DashboardSummaryInfoTypes.TotalDids,
  },
  {
    icon: "/assets/Wallet.svg",
    title: "Wallet addresses",
    valueKey: DashboardSummaryInfoTypes.TotalWallets,
  },
  {
    icon: "/assets/Transactions.svg",
    title: "Transactions",
    valueKey: DashboardSummaryInfoTypes.TotalTransactions,
  },
  {
    icon: "/assets/Coins.svg",
    title: "Tokens burned",
    valueKey: DashboardSummaryInfoTypes.TotalBurnedCoins,
    format: (value: number) => ({
      amount: value / 1_000_000,
      unit: "EMPE",
    }),
    isFlexRow: true,
  },
  {
    icon: "/assets/Cash.svg",
    title: "Protocol Revenue",
    valueKey: DashboardSummaryInfoTypes.ProtocolRevenue,
    format: (value: number) => ({
      amount: Math.round(value / 1_000_000),
      unit: "EMPE",
    }),
  },
];

export const SummaryInfo = () => {
  const { summaryCount } = useSummaryCount();

  const itemList = summaryInfo.map((info, index) => {
    const isLast = index === summaryInfo.length - 1;
    const rawValue = summaryCount[info.valueKey] || 0;
    const value = info.format ? info.format(rawValue) : rawValue;

    return (
      <Fragment key={info.valueKey}>
        <SummaryItem icon={info.icon} title={info.title} value={value} />
        {!isLast && (
          <div className="hidden lg:flex flex-1 items-center justify-center min-h-[50px] h-full">
            <div className="h-[50px] w-[1px] bg-main-900" />
          </div>
        )}
      </Fragment>
    );
  });

  return (
    <div className="flex flex-wrap justify-center lg:h-full items-center lg:justify-evenly gap-4 px-10">
      {itemList}
    </div>
  );
};
