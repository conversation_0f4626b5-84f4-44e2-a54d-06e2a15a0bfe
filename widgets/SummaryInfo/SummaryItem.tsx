import Image from "next/image";

export const SummaryItem = ({
  icon,
  title,
  value,
}: {
  icon: string;
  title: string;
  value: string | { amount: number; unit: string };
}) => (
  <div className="flex gap-6 px-2 my-auto self-center flex-1 text-left justify-center lg:flex-row flex-col items-center">
    <Image src={icon} alt={title} width={32} height={32} />
    <div className="space-y-2 flex flex-col justify-center md:items-start">
      <div className="text-xs font-semibold whitespace-nowrap text-center">
        {title}
      </div>
      {typeof value === "object" ? (
        <div className="lg:text-lg text-main-600 flex flex-row gap-1 text-center md:text-left">
          {value.amount} <span>{value.unit}</span>
        </div>
      ) : (
        <div className="lg:text-lg text-main-600 text-center lg:text-left">
          {value}
        </div>
      )}
    </div>
  </div>
);
