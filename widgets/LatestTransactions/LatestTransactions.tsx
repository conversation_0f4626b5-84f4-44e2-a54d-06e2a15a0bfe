import DashboardBoxHeader from "@/components/DashboardBoxHeader";
import { LineSeparator } from "@/components/Separators";
import React, { Fragment } from "react";
import { LatestTransactionsItem } from "./LatestTransactionsItem";
import { useTransactions } from "@/hooks/dashboard/useTransactions";
import { useExplorerUrl } from "@/hooks/dashboard/useExploreUrl";

export const LatestTransactions = () => {
  const { transactions } = useTransactions();
  const { explorer } = useExplorerUrl();
  const urlValid =
    explorer && explorer.url ? `${explorer.url}/transactions` : false;

  const itemsList = transactions.map((transaction, index) => {
    const isLastItem = index === transactions.length - 1;
    return (
      <Fragment key={`transaction-${transaction.hash}-${index}`}>
        <LatestTransactionsItem
          id={transaction.hash}
          time={transaction.block.timestamp}
          href={urlValid && `${urlValid}/${transaction.hash}`}
          message={transaction.messages}
        />
        {!isLastItem && <LineSeparator />}
      </Fragment>
    );
  });

  return (
    <div className="text-main-600 h-full">
      <DashboardBoxHeader
        title="Latest transactions"
        showAllCallback={urlValid && `${urlValid}`}
      />
      <div className="overflow-y-auto without-scrollbar max-h-[500px]">
        {itemsList}
      </div>
    </div>
  );
};
