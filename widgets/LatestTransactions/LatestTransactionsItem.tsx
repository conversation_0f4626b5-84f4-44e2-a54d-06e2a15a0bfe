import { cutLongString } from "@/utils/cutLongString";
import { formatDateTime } from "@/utils/formatDateTime";
import Link from "next/link";
import { FaCube } from "react-icons/fa6";

interface Props {
  id: string;
  time: string;
  href: string;
  message?: Array<{
    "@type": string;
    sender: string;
    signatures: Array<{
      methodId: string;
      sigBytes: string;
    }>;
  }>;
}

export const LatestTransactionsItem = ({
  id,
  time,
  href,
  message = [],
}: Props) => {
  const cuttedId = cutLongString(id);
  const formattedTime = formatDateTime(time);

  const formatMessageType = (type: string) => {
    if (!type) return "Unknown";
    const withoutMsg = type.replace(/^Msg/, "");
    return withoutMsg.replace(/([a-z])([A-Z])/g, "$1 $2");
  };

  const messageType = message?.[0]?.["@type"]?.split(".").pop();
  const formattedMessageType = formatMessageType(messageType);

  return (
    <Link
      href={href}
      target="_blank"
      className="flex items-center justify-between py-4 gap-2 hover:opacity-60"
    >
      <div className="flex flex-[10] items-center">
        <div className="w-10 h-10 bg-gradient-to-r p-[1px] from-main-200 to-main-300 rounded-full flex items-center justify-center">
          <div className="w-full h-full bg-main-900 rounded-full flex items-center justify-center">
            <FaCube className="text-lg" />
          </div>
        </div>
        <div className="flex flex-col justify-start items-start">
          <span className="ml-4 text-main-100">{cuttedId}</span>
          <div className="text-xs text-left md:text-center whitespace-nowrap flex flex-col md:flex-row gap-1 px-4">
            <span>{formattedTime.date}</span>
            <span>{formattedTime.time}</span>
          </div>
        </div>
      </div>
      <div className="flex flex-col md:flex-row justify-start items-center text-xs md:text-lg gap-2 ">
        <p className="bg-main-100/10 p-1 text-center rounded-md text-main-100">
          {formattedMessageType}
        </p>
        <p>+{message?.length || 0}</p>
      </div>
    </Link>
  );
};
