import { useDashboardStore } from "@/store/dashboardStore";
import { calculateAPR } from "@/utils/calculateAPR";
import Image from "next/image";

export const StakingAPR = () => {
  const { inflation, totalSupply, bondedTokens } = useDashboardStore();

  if (!inflation || !totalSupply || !bondedTokens) return null;

  const apr = calculateAPR(inflation, totalSupply, bondedTokens);

  const displayApr = apr.toFixed(2);

  return (
    <div className="flex items-center h-full px-4 text-main-100 w-full relative whitespace-nowrap gap-2">
      <div className="border-r border-white/10 pr-4 h-full flex items-center">
        <Image
          src="/assets/precentage_blue.svg"
          alt="Staking apr"
          width={32}
          height={32}
          className="min-w-[32px] min-h-[32px]"
        />
      </div>
      <div className="flex flex-col items-start text-left pl-4">
        <div className="text-sm text-left w-full">Staking APR:</div>
        <div className="text-2xl text-left w-full">{displayApr}%</div>
      </div>
    </div>
  );
};
