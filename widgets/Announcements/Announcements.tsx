import DashboardBoxHeader from "@/components/DashboardBoxHeader";
import { LineSeparator } from "@/components/Separators";
import React, { Fragment } from "react";
import { AnnouncementsItem } from "./AnnouncementsItem";
import getDiscordMessages from "@/api/getDiscordMessages";
import { DiscordMessage } from "@/types/discordMessage";
import { useQuery } from "@tanstack/react-query";
import {
  prepareDiscordLinkToChannel,
  prepareDiscordLinkToMessage,
} from "@/utils/prepareDiscordLinks";

export const Announcements = () => {
  const { data: messages, error } = useQuery<DiscordMessage[], Error>(
    ["discordMessages"],
    getDiscordMessages,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 10, // 10 minutes
    }
  );

  if (error) {
    console.error(error);
  }

  const itemsList = () =>
    messages.slice(0, 6)?.map((announcement, index) => {
      const isLastItem = index === messages.length - 1;
      return (
        <Fragment key={`announcement-${announcement.id}-${index}`}>
          <AnnouncementsItem
            title={announcement.author.username}
            description={announcement.content}
            link={prepareDiscordLinkToMessage(
              announcement.id,
              announcement.channel_id
            )}
            imageUrl={announcement.attachments[0]?.url}
          />
          {!isLastItem && <LineSeparator />}
        </Fragment>
      );
    });

  const isMessagesExist = Boolean(
    !messages || "error" in messages || messages.length === 0
  );

  return (
    <div className="text-white h-full">
      <DashboardBoxHeader
        title="Announcements"
        showAllCallback={prepareDiscordLinkToChannel()}
      />
      <ul className="overflow-y-auto without-scrollbar max-h-[500px]">
        {isMessagesExist ? (
          <div className="text-center text-gray-500">
            No announcements found
          </div>
        ) : (
          itemsList()
        )}
      </ul>
    </div>
  );
};
