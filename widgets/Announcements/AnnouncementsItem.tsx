import Image from "next/image";
import Link from "next/link";
interface Props {
  title: string;
  description: string;
  link: string;
  imageUrl?: string;
}
export const AnnouncementsItem = ({
  title,
  description,
  link,
  imageUrl,
}: Props) => {
  return (
    <li className="flex flex-col py-4">
      <div className="flex items-center gap-2 mb-2">
        <Image src="/assets/rocket.svg" alt="Rocket" width={24} height={24} />
        <h3 className="text-main-100">{title}</h3>
      </div>
      <p className="whitespace-pre-wrap">{description.slice(0, 100) + "..."}</p>
      {imageUrl && (
        <div className="flex mt-2 justify-start">
          <Image
            src={imageUrl}
            alt="Announcement"
            className="w-1/2"
            width={500}
            height={500}
          />
        </div>
      )}
      <Link
        href={link}
        className="text-main-100 mt-1 self-end text-xs underline"
        target="_blank"
      >
        More info
      </Link>
    </li>
  );
};
