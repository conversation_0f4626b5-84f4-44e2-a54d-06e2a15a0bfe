import { LineSeparator, LineSeparatorVertical } from "@/components/Separators";
import React from "react";
import { SupplyBottomSummary } from "./SupplyBottomSummary";
import { SupplyChartSummary } from "./SupplyChartSummary";
import { PieChartSVG } from "./PieChartSVG";
import { useDashboardStore } from "@/store/dashboardStore";

export const Supply = ({}: {}) => {
  const { totalSupply, communityPool, bondedTokens, circulatingSupply } =
    useDashboardStore();

  if (!totalSupply || !bondedTokens || !communityPool) return null;

  const bondedPercentage = Math.round(
    (Number(bondedTokens) / Number(totalSupply)) * 100
  );

  return (
    <div className="w-full h-full text-main-600 flex flex-col gap-2">
      <h2 className="text-lg font-semibold mb-4">Supply:</h2>
      <LineSeparator />
      <div className="flex flex-col lg:flex-row">
        <div className="flex flex-row gap-2 min-w-[100px]">
          <PieChartSVG bondedPercentage={bondedPercentage} />
        </div>
        <div className="flex flex-col md:flex-row flex-1 gap-4">
          <div className="flex-1 md:pl-16">
            <SupplyChartSummary
              totalSupplyAmount={totalSupply}
              circulatingSupplyAmount={circulatingSupply}
            />
          </div>
          <LineSeparatorVertical />
          <div className="flex-1 md:pl-16">
            <SupplyBottomSummary
              communityPoolAmount={communityPool}
              bondedTokens={bondedTokens}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
