interface Props {
  bondedPercentage: number;
}

export const PieChartSVG = ({ bondedPercentage }: Props) => {
  const radius = 40;
  const circumference = 2 * Math.PI * radius;
  const bondedStroke = (bondedPercentage / 100) * circumference;
  const unbondedStroke = circumference - bondedStroke;

  return (
    <svg viewBox="0 0 100 100" className="w-full max-h-[200px] h-auto">
      <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{ stopColor: "#00afff", stopOpacity: 1 }} />
          <stop
            offset="100%"
            style={{ stopColor: "#e200ed", stopOpacity: 1 }}
          />
        </linearGradient>
      </defs>
      <circle
        cx="50"
        cy="50"
        r={radius}
        fill="none"
        stroke="#2D2E32"
        strokeWidth="10"
      />
      <circle
        cx="50"
        cy="50"
        r={radius}
        fill="none"
        stroke="url(#gradient)"
        strokeWidth="20"
        strokeDasharray={`${bondedStroke} ${unbondedStroke}`}
        strokeDashoffset={circumference}
        transform="rotate(-90 50 50)"
      />
    </svg>
  );
};
