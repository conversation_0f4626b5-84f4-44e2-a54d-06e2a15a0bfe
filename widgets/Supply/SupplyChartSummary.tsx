import { SupplyCount } from "./SupplyCount";
import { formatNumberToDisplay } from "@/utils/formatNumberToDisplay";

interface Props {
  totalSupplyAmount: string;
  circulatingSupplyAmount: string;
}

export const SupplyChartSummary = ({
  totalSupplyAmount,
  circulatingSupplyAmount,
}: Props) => {
  const displayTotalSupplyAmount = formatNumberToDisplay(totalSupplyAmount);
  const displayCirculatingSupplyAmount = formatNumberToDisplay(
    circulatingSupplyAmount
  );

  return (
    <div className="flex-[2] flex flex-col justify-evenly h-full gap-4">
      <div className="flex flex-col items-start gap-2 text-md">
        <div className="text-xl">Circulating supply:</div>
        <div className="text-4xl space-x-2">
          <SupplyCount count={displayCirculatingSupplyAmount} label="EMPE" />
        </div>
      </div>
      <div className="space-y-2">
        <span className="text-sm">Total supply:</span>
        <div className="text-lg space-x-2">
          <span>{displayTotalSupplyAmount}</span>
          <span className=" text-main-800">EMPE</span>
        </div>
      </div>
    </div>
  );
};
