import { formatNumberToDisplay } from "@/utils/formatNumberToDisplay";
import { SupplyCount } from "./SupplyCount";

interface Props {
  communityPoolAmount: string;
  bondedTokens: string;
}

export const SupplyBottomSummary = ({
  communityPoolAmount,
  bondedTokens,
}: Props) => {
  const displayCommunityPoolAmount = formatNumberToDisplay(
    communityPoolAmount,
    "en-US",
    undefined,
    10 ** 18
  );

  const displayBondedTokens = formatNumberToDisplay(bondedTokens);

  return (
    <div className="flex flex-col gap-4 h-full justify-evenly">
      <div className="flex flex-col items-start gap-2 text-md">
        <div className="text-sm">Community pool:</div>
        <SupplyCount count={displayCommunityPoolAmount} label="EMPE" />
      </div>
      <div className="flex flex-col space-y-2 items-start mt-2 text-md">
        <span className="text-sm">Staked:</span>
        <div className="flex items-center text-xl space-x-2">
          <SupplyCount count={displayBondedTokens} label="EMPE" />
        </div>
      </div>
    </div>
  );
};
