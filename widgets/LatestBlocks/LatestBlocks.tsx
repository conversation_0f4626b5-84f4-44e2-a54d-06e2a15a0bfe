import React from "react";
import DashboardBoxHeader from "@/components/DashboardBoxHeader";
import { useBlocks } from "@/hooks/dashboard/useBlocks";
import { useExplorerUrl } from "@/hooks/dashboard/useExploreUrl";
import { LatestBlockMapItem } from "./LatestBlockMapItem";

export const LatestBlocks = () => {
  const { blocks } = useBlocks();
  const { explorer } = useExplorerUrl();
  const urlValid = explorer && explorer.url ? `${explorer.url}/blocks` : false;

  const itemsList = blocks.map((block, index) => {
    const isLastItem = index === blocks.length - 1;
    return (
      <LatestBlockMapItem
        key={`block-${block.hash}-${index}`}
        block={block}
        isLastItem={isLastItem}
        urlValid={urlValid}
      />
    );
  });

  return (
    <div className="w-full text-main-600 h-full">
      <DashboardBoxHeader
        title="Latest blocks"
        showAllCallback={urlValid && `${urlValid}`}
      />
      <ul className="overflow-y-auto without-scrollbar max-h-[500px]">
        {itemsList}
      </ul>
    </div>
  );
};
