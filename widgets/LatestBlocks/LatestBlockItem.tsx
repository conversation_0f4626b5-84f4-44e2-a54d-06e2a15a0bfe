import { cutLongString } from "@/utils/cutLongString";
import { formatDateTime } from "@/utils/formatDateTime";
import Link from "next/link";
import React from "react";
import { FaCube } from "react-icons/fa";
import { DisplayValidatorNameWithIcon } from "@/components/DisplayValidatorNameWithIcon";

interface LatestBlockItemProps {
  blockNumber: string;
  validatorName: string;
  validatorAvatar: string | null;
  txCount: number;
  timeAgo: string;
  href: string;
}

export const LatestBlockItem: React.FC<LatestBlockItemProps> = ({
  blockNumber,
  validatorName,
  validatorAvatar,
  txCount,
  timeAgo,
  href,
}) => {
  const cuttedBlockNumber = cutLongString(blockNumber);
  const formattedTime = formatDateTime(timeAgo);

  return (
    <Link
      href={href}
      target="_blank"
      className="flex items-center py-4 gap-1 justify-between hover:opacity-60"
    >
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-gradient-to-r p-[1px] from-main-200 to-main-300 rounded-full flex items-center justify-center">
          <div className="w-full h-full bg-main-900 rounded-full flex items-center justify-center">
            <FaCube className="text-white text-lg" />
          </div>
        </div>
        <div className="space-y-1">
          <span className="text-main-100 font-bold block">
            #{cuttedBlockNumber}
          </span>
          <div className="text-sm text-gray-400 flex items-center gap-2">
            <span className="whitespace-nowrap hidden md:block">
              Validated by:{" "}
            </span>
            <DisplayValidatorNameWithIcon
              validatorName={validatorName}
              validatorAvatar={validatorAvatar}
            />
          </div>
        </div>
      </div>
      <div className="text-center space-y-1 whitespace-nowrap">
        <div className="text-white">{txCount} Txs</div>
        <div className="text-xs text-gray-400 flex flex-col">
          <span>{formattedTime.date}</span>
          <span>{formattedTime.time}</span>
        </div>
      </div>
    </Link>
  );
};
