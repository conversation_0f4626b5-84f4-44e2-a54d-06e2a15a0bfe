import { Fragment } from "react";
import { LatestBlockItem } from "./LatestBlockItem";
import { LineSeparator } from "@/components/Separators";

interface Props {
  block: any;
  isLastItem: boolean;
  urlValid: string | false;
}
export const LatestBlockMapItem = ({ block, isLastItem, urlValid }: Props) => {
  const validatorDescriptions =
    block?.validator?.validatorInfo?.validator?.validator_descriptions;

  let validatorName = "Unknown Validator";
  let validatorAvatar = null;

  if (block?.validator?.validatorInfo?.operatorAddress) {
    validatorName = block.validator.validatorInfo.operatorAddress;
  }

  if (
    validatorDescriptions &&
    validatorDescriptions.length > 0 &&
    validatorDescriptions[0]
  ) {
    if (validatorDescriptions[0].moniker) {
      validatorName = validatorDescriptions[0].moniker;
    }

    if (validatorDescriptions[0].avatar_url) {
      validatorAvatar = validatorDescriptions[0].avatar_url;
    }
  }

  return (
    <Fragment>
      <LatestBlockItem
        blockNumber={block.hash}
        validatorName={validatorName}
        validatorAvatar={validatorAvatar}
        txCount={block.txs}
        timeAgo={block.timestamp}
        href={urlValid && `${urlValid}/${block.height}`}
      />
      {!isLastItem && <LineSeparator />}
    </Fragment>
  );
};
