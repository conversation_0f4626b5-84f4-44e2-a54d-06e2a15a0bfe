# syntax=docker.io/docker/dockerfile:1

FROM node:20-alpine AS base

# Wspólne zmienne środowiskowe dla wszystkich etapów
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Kopiuj tylko pliki potrzebne do instalacji zależności
COPY empe-front-kit/packages/next-ui/package.json /app/empe-front-kit/packages/next-ui/
COPY empe-front-kit/packages/tailwind-config/package.json /app/empe-front-kit/packages/tailwind-config/
COPY empe-hub/package.json empe-hub/yarn.lock* /app/

# Instaluj zależności z cache'owaniem i czyszczeniem cache
RUN yarn install --production=false --network-timeout 600000 && \
    yarn cache clean

# Teraz kopiuj resztę lokalnych bibliotek
COPY empe-front-kit/packages/next-ui /app/empe-front-kit/packages/next-ui
COPY empe-front-kit/packages/tailwind-config /app/empe-front-kit/packages/tailwind-config

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Kopiuj tylko niezbędne pliki z etapu deps
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/empe-front-kit ./empe-front-kit

# Kopiuj pliki projektu - tylko te potrzebne do budowania
COPY empe-hub/public ./public
COPY empe-hub/components ./components
COPY empe-hub/config ./config
COPY empe-hub/contexts ./contexts
COPY empe-hub/enums ./enums
COPY empe-hub/hooks ./hooks
COPY empe-hub/pages ./pages
COPY empe-hub/providers ./providers
COPY empe-hub/store ./store
COPY empe-hub/styles ./styles
COPY empe-hub/types ./types
COPY empe-hub/utils ./utils
COPY empe-hub/widgets ./widgets
COPY empe-hub/api ./api
COPY empe-hub/next.config.js ./
COPY empe-hub/postcss.config.js ./
COPY empe-hub/tailwind.config.ts ./
COPY empe-hub/tsconfig.json ./
COPY empe-hub/declaration.d.ts ./
COPY empe-hub/next-env.d.ts ./
COPY empe-hub/package.json ./

# Wyłącz telemetrię Next.js i zwiększ limit pamięci dla Node.js
ENV NEXT_IGNORE_BUILD_ERRORS=1
ENV NEXT_DISABLE_TYPECHECK=1

# Zwiększ limit pamięci dla Node.js i użyj cache'a dla szybszego budowania
RUN \
  NODE_OPTIONS="--max-old-space-size=4096" \
  yarn run build && \
  # Usuń niepotrzebne pliki po budowaniu
  rm -rf .next/cache && \
  # Usuń pliki źródłowe TypeScript, które nie są potrzebne w produkcji
  find . -name "*.ts" -not -name "*.d.ts" -delete && \
  find . -name "*.tsx" -delete && \
  # Usuń inne niepotrzebne pliki
  rm -rf node_modules/.cache

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Kopiuj tylko niezbędne pliki produkcyjne
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
CMD ["node", "server.js"]