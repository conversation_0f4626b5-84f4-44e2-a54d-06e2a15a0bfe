import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useCallback,
} from "react";
import { useDisclosure, Prices } from "@/hooks";
import { type ExtendedValidator as Validator } from "@/utils";
import { ChainName } from "cosmos-kit";
import { DelegateModal } from "@/components/staking/DelegateModal";
import { UndelegateModal } from "@/components/staking/UndelegateModal";
import { RedelegateModal } from "@/components/staking/RedelegateModal";
import { ValidatorInfoModal } from "@/components/staking/ValidatorInfoModal";

// Interface for staking configuration
interface StakingConfig {
  balance: string;
  updateData: () => void;
  unbondingDays: string;
  chainName: ChainName;
  logos: { [key: string]: string };
  prices: Prices;
  allValidators: Validator[];
}

// Interface for context
interface StakingModalsContextType {
  // Configuration
  setStakingConfig: (config: StakingConfig) => void;
  stakingConfig: StakingConfig | null;

  // Validators
  selectedValidator: Validator | undefined;
  validatorToRedelegate: Validator | undefined;

  openValidatorDetailsSubpage: (validator: Validator) => void;
  // Methods for opening modals
  openValidatorInfoModal: (validator: Validator) => void;
  openDelegateModal: (
    validator: Validator,
    closeOuterModal?: () => void
  ) => void;
  openUndelegateModal: (
    validator: Validator,
    closeOuterModal?: () => void
  ) => void;
  openSelectValidatorModal: (validator: Validator) => void;
  openRedelegateModal: (
    fromValidator: Validator,
    toValidator: Validator
  ) => void;

  // Method for rendering all modals
  renderModals: () => JSX.Element;
}

// Creating the context
const StakingModalsContext = createContext<
  StakingModalsContextType | undefined
>(undefined);

// Context provider
export const StakingModalsProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // Configuration state
  const [stakingConfig, setStakingConfigState] = useState<StakingConfig | null>(
    null
  );

  // In StakingModalsContext.tsx
  const setStakingConfig = useCallback((config: StakingConfig) => {
    setStakingConfigState((prevConfig) => {
      // Compare previous and new configuration to avoid unnecessary updates
      if (prevConfig && JSON.stringify(prevConfig) === JSON.stringify(config)) {
        return prevConfig;
      }
      return config;
    });
  }, []);

  // Validators state
  const [selectedValidator, setSelectedValidator] = useState<
    Validator | undefined
  >();
  const [validatorToRedelegate, setValidatorToRedelegate] = useState<
    Validator | undefined
  >();
  const [closeOuterModalFn, setCloseOuterModalFn] = useState<
    (() => void) | undefined
  >();

  // Controllers for modals
  const validatorInfoModal = useDisclosure();
  const delegateModal = useDisclosure();
  const undelegateModal = useDisclosure();
  const selectValidatorModal = useDisclosure();
  const redelegateModal = useDisclosure();

  const openValidatorDetailsSubpage = (validator: Validator) => {
    setSelectedValidator(validator);
  };

  // Methods for opening modals
  const openValidatorInfoModal = (validator: Validator) => {
    setSelectedValidator(validator);
    validatorInfoModal.onOpen();
  };

  const openDelegateModal = (
    validator: Validator,
    closeOuterModal?: () => void
  ) => {
    setSelectedValidator(validator);
    if (closeOuterModal) {
      setCloseOuterModalFn(() => closeOuterModal);
    } else {
      setCloseOuterModalFn(undefined);
    }
    delegateModal.onOpen();
  };

  const openUndelegateModal = (
    validator: Validator,
    closeOuterModal?: () => void
  ) => {
    setSelectedValidator(validator);
    if (closeOuterModal) {
      setCloseOuterModalFn(() => closeOuterModal);
    } else {
      setCloseOuterModalFn(undefined);
    }
    undelegateModal.onOpen();
  };

  const openSelectValidatorModal = (validator: Validator) => {
    setSelectedValidator(validator);
    selectValidatorModal.onOpen();
  };

  const openRedelegateModal = (
    fromValidator: Validator,
    toValidator: Validator
  ) => {
    setSelectedValidator(fromValidator);
    setValidatorToRedelegate(toValidator);
    redelegateModal.onOpen();
  };

  // Method for rendering all modals
  const renderModals = () => {
    if (!stakingConfig) return null;

    const {
      balance,
      updateData,
      unbondingDays,
      chainName,
      logos,
      prices,
      allValidators,
    } = stakingConfig;

    return (
      <>
        {/* ValidatorInfoModal */}
        {selectedValidator && validatorInfoModal.isOpen && (
          <ValidatorInfoModal
            chainName={chainName}
            logoUrl={logos[selectedValidator.address]}
            modalControl={validatorInfoModal}
            selectedValidator={selectedValidator}
            handleClick={{
              openValidatorDetailsSubpage: () =>
                openValidatorDetailsSubpage(selectedValidator),
              openDelegateModal: () =>
                openDelegateModal(
                  selectedValidator,
                  validatorInfoModal.onClose
                ),
              openSelectValidatorModal: () =>
                openSelectValidatorModal(selectedValidator),
              openUndelegateModal: () =>
                openUndelegateModal(
                  selectedValidator,
                  validatorInfoModal.onClose
                ),
            }}
          />
        )}

        {/* DelegateModal */}
        {selectedValidator && delegateModal.isOpen && (
          <DelegateModal
            openValidatorDetailsSubpage={() =>
              openValidatorDetailsSubpage(selectedValidator)
            }
            balance={balance}
            chainName={chainName}
            closeOuterModal={closeOuterModalFn}
            modalControl={delegateModal}
            selectedValidator={selectedValidator}
            logoUrl={logos[selectedValidator.address]}
            unbondingDays={unbondingDays}
            updateData={updateData}
            showDescription={!closeOuterModalFn} // Pokazuj opis tylko gdy nie ma nadrzędnego modalu
            prices={prices}
          />
        )}

        {/* UndelegateModal */}
        {selectedValidator && undelegateModal.isOpen && (
          <UndelegateModal
            openValidatorDetailsSubpage={() =>
              openValidatorDetailsSubpage(selectedValidator)
            }
            chainName={chainName}
            closeOuterModal={closeOuterModalFn}
            modalControl={undelegateModal}
            selectedValidator={selectedValidator}
            logoUrl={logos[selectedValidator.address]}
            unbondingDays={unbondingDays}
            updateData={updateData}
          />
        )}

        {/* SelectValidatorModal */}
        {/* MODAL is not used becasue logic of modal is transferred to subpage */}
        {/* {selectValidatorModal.isOpen && (
          <SelectValidatorModal
            chainName={chainName}
            modalControl={selectValidatorModal}
            logos={logos}
            allValidators={allValidators}
            handleValidatorClick={(validator) => {
              redelegateModal.onOpen();
              selectValidatorModal.onClose();
              setValidatorToRedelegate(validator);
            }}
          />
        )} */}

        {/* RedelegateModal */}
        {selectedValidator &&
          validatorToRedelegate &&
          redelegateModal.isOpen && (
            <RedelegateModal
              openValidatorDetailsSubpage={() =>
                openValidatorDetailsSubpage(selectedValidator)
              }
              logoUrl={logos[selectedValidator.address]}
              chainName={chainName}
              balance={balance}
              modalControl={redelegateModal}
              selectedValidator={selectedValidator}
              validatorToRedelegate={validatorToRedelegate}
              updateData={updateData}
              prices={prices}
            />
          )}
      </>
    );
  };

  return (
    <StakingModalsContext.Provider
      value={{
        openValidatorDetailsSubpage,
        setStakingConfig,
        stakingConfig,
        selectedValidator,
        validatorToRedelegate,
        openValidatorInfoModal,
        openDelegateModal,
        openUndelegateModal,
        openSelectValidatorModal,
        openRedelegateModal,
        renderModals,
      }}
    >
      {children}
    </StakingModalsContext.Provider>
  );
};

// Hook to use context
export const useStakingModals = () => {
  const context = useContext(StakingModalsContext);
  if (!context) {
    throw new Error(
      "useStakingModals must be used within a StakingModalsProvider"
    );
  }
  return context;
};
