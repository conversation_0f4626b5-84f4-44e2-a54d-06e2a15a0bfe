import { useDashboard } from "@/hooks/dashboard/useDashboard";
import { useDashboardStore } from "@/store/dashboardStore";
import { PageSizeLoader } from "@empe/front-kit-next-ui";

export const MainDataProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { isLoading } = useDashboardStore();
  useDashboard();

  if (isLoading) return <PageSizeLoader />;
  return <>{children}</>;
};
