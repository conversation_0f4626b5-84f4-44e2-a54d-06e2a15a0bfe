import { AirdropEvent } from "@/types/airdropEvents";

export const airdropEvents: AirdropEvent[] = [
  {
    image: "/airdrop/chain_1.png",
    onClick: () => {},
    title: "On-Chain Testnet Airdrop",
    links: [
      {
        label: "Learn more from documentation",
        url: "https://docs.empe.io/user-guide/airdrop/on-chain-testnet-airdrop",
      },
      {
        label: "Guide: How to claim testnet tokens",
        url: "https://docs.empe.io/user-guide/airdrop/faucet-guide-how-to-claim-testnet-tokens",
      },
    ],
    type: "Automatic",
    status: "Ongoing",
    buttonText: "Claim Test Tokens to start",
  },
  {
    image: "/airdrop/gempe.png",
    onClick: () => {},
    title: "gEMPE TG Airdrop",
    description:
      "Play on Telegram, invite friends and complete missions to gain leadeboard’s position and allocation in the upcoming airdrop",
    type: "TG Mini-App",
    status: "Ongoing",
    buttonText: "Join Now",
  },
];
