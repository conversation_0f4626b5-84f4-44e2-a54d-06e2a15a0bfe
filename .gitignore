# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

/.idea/
# dependencies
/node_modules
/.pnp
.pnp.js

/dist
# testing
/coverage

# next.js
/.next/
/out/

#empejs
/empejs
/empejs/
/empejs/**
/empejs/*

./empejs
./empejs/
./empejs/**
./empejs/*

#empe-front-kit
/empe-front-kit
/empe-front-kit/
/empe-front-kit/**
/empe-front-kit/*

./empe-front-kit
./empe-front-kit/
./empe-front-kit/**
./empe-front-kit/*


# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.qodo


./vscode
.vscode
.vscode/*
.vscode/**