import sharedConfig from "@empe/front-kit-tailwind-config/tailwind.config";

export default {
  ...sharedConfig,

  content:
    process.env.NODE_ENV === "development"
      ? [
          ...sharedConfig.content.filter(
            (pattern: string) => !pattern.includes("./dist/")
          ),
          "./utils/**/*.{ts,tsx,js,jsx}", // Add this line
        ]
      : [
          ...sharedConfig.content,
          "./utils/**/*.{ts,tsx,js,jsx}", // Add this line
        ],
};
