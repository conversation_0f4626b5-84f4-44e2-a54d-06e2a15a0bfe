apiVersion: apps/v1
kind: Deployment
metadata:
  name: empe-hub-prod
  namespace: customer-issuer-verifier
spec:
  replicas: 1
  selector:
    matchLabels:
      app: empe-hub-prod
  template:
    metadata:
      labels:
        app: empe-hub-prod
    spec:
      containers:
        - name: empe-hub
          image: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/empehub:0.0.65
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
      imagePullSecrets:
        - name: harbor-registry-secret
