.bg-gradient {
  background: rgb(0, 175, 255);
  background: -moz-linear-gradient(
    135deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  background: -webkit-linear-gradient(
    135deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  background: linear-gradient(
    135deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#00afff",endColorstr="#e200ed",GradientType=1);
}

.item-gradient {
  background: -moz-linear-gradient(
    90deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  background: -webkit-linear-gradient(
    90deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  background: linear-gradient(
    90deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#00afff",endColorstr="#e200ed",GradientType=1);
}

.item-gradient:disabled {
  background: #2d2e32 !important;
}

.border-gradient {
  position: relative;
  background: transparent;
  z-index: 1;
  border: none;
}

.border-gradient::after {
  content: "";
  position: absolute;
  background: transparent;
  pointer-events: none;
  inset: 0;
  padding: 1px; /* Grubość bordera */
  border-radius: inherit;
  background: linear-gradient(
    90deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.border-gradient-hover {
  position: relative;
  background: transparent;
  z-index: 1;
}

/* .border-gradient-hover:hover {
  border: none;
} */

.border-gradient-hover:hover::after {
  content: "";
  position: absolute;
  background: transparent;
  pointer-events: none;
  inset: 0;
  padding: 1px; /* Grubość bordera */
  border-radius: inherit;
  background: linear-gradient(
    90deg,
    rgba(0, 175, 255, 1) 0%,
    rgba(0, 110, 255, 1) 50%,
    rgba(226, 0, 237, 1) 100%
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}
