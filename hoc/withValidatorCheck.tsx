// components/withValidatorCheck.tsx
import { useRouter } from "next/router";
import { useEffect } from "react";
import { useCheckIsValidator } from "@/hooks/useCheckIsValidator"; // Adjust path as needed

export function withValidatorCheck<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> {
  return function WithValidatorCheck(props: P) {
    const router = useRouter();
    const { isValidator, isLoading } = useCheckIsValidator();

    useEffect(() => {
      if (!isValidator && !isLoading) {
        router.push("/");
      }
    }, [isValidator, isLoading, router]);

    if (!isValidator || isLoading) {
      return null;
    }

    return <Component {...props} />;
  };
}
